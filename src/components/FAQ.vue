<template>
  <!-- FAQ Section -->
  <div class="mt-4" data-aos="fade-up">
    <h3 class="text-2xl font-bold text-center mb-6">Foire aux questions</h3>

    <div class="max-w-4xl mx-auto divide-y divide-gray-200 rounded-lg bg-gray-50 shadow-sm">
      <!-- FAQ Item 1 -->
      <div class="p-3 hover:bg-gray-100 transition-colors duration-200 rounded-t-lg">
        <button
          class="flex justify-between items-center w-full text-left"
          @click="toggleFaq('faq1')"
        >
          <h4 class="text-lg font-semibold text-gray-800 flex items-center">
            <i class="fas fa-shield-alt mr-3 text-blue-600"></i>Mes données sont-elles protégées ?
          </h4>
          <i
            class="fas fa-chevron-down text-gray-500 transition-transform"
            :class="{ 'rotate-180': openFaqs.faq1 }"
          ></i>
        </button>
        <div v-show="openFaqs.faq1" class="mt-2 text-gray-600">
          <p class="mb-1">
            Nous ne conservons aucune donnée autre que celles strictement nécessaires à votre identification.
            Ni vos questions ni les réponses générées ne sont stockées sur nos serveurs.
          </p>

          <p class="mb-1">
            Votre confidentialité est notre priorité absolue. Pour garder une trace de votre consultation juridique,
            nous vous recommandons de télécharger votre fiche de réponse en utilisant le bouton prévu à cet effet.
          </p>

          <p>
            Une fois votre session terminée, toutes les données liées à votre consultation sont définitivement
            effacées de notre système.
          </p>
        </div>
      </div>

      <!-- FAQ Item 2 -->
      <div class="p-3 hover:bg-gray-100 transition-colors duration-200">
        <button
          class="flex justify-between items-center w-full text-left"
          @click="toggleFaq('faq2')"
        >
          <h4 class="text-lg font-semibold text-gray-800 flex items-center">
            <i class="fas fa-robot mr-3 text-blue-600"></i>Comment fonctionne QuestionLegale.Info ?
          </h4>
          <i
            class="fas fa-chevron-down text-gray-500 transition-transform"
            :class="{ 'rotate-180': openFaqs.faq2 }"
          ></i>
        </button>
        <div v-show="openFaqs.faq2" class="mt-2 text-gray-600">
          <p class="mb-1">
            QuestionLegale.Info utilise l'état de l'art en matière de traitement du langage naturel (LLM)
            et d'intelligence artificielle. Notre moteur de réponse est régulièrement mis à jour pour intégrer
            les dernières avancées technologiques.
          </p>

          <p class="mb-1">
            Grâce à ces technologies de pointe, nous analysons rapidement votre situation juridique,
            extrayons les éléments pertinents de votre demande et vous fournissons des informations
            précises et adaptées.
          </p>

          <p>
            Notre système continue d'apprendre et de s'améliorer, garantissant des réponses toujours
            plus pertinentes et des orientations juridiques de qualité.
          </p>
        </div>
      </div>

      <!-- FAQ Item 3 -->
      <div class="p-3 hover:bg-gray-100 transition-colors duration-200 rounded-b-lg">
        <button
          class="flex justify-between items-center w-full text-left"
          @click="toggleFaq('faq3')"
        >
          <h4 class="text-lg font-semibold text-gray-800 flex items-center">
            <i class="fas fa-ad mr-3 text-red-600"></i>Comment faire de la publicité sur le site ?
          </h4>
          <i
            class="fas fa-chevron-down text-gray-500 transition-transform"
            :class="{ 'rotate-180': openFaqs.faq3 }"
          ></i>
        </button>
        <div v-show="openFaqs.faq3" class="mt-2 text-gray-600">
          <p>
            Si vous êtes un professionnel du droit et souhaitez mettre en avant vos services sur notre plateforme,
            contactez notre équipe commerciale. Nous proposons différentes formules de partenariat pour vous aider
            à toucher notre audience de personnes intéressées par des services juridiques.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const openFaqs = ref({
  faq1: false,
  faq2: false,
  faq3: false
})

const toggleFaq = (faqId) => {
  openFaqs.value[faqId] = !openFaqs.value[faqId]
}
</script>

<style scoped>
.rotate-180 {
  transform: rotate(180deg);
}
</style>
