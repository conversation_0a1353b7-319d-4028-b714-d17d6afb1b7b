<template>
  <!-- <PERSON> professionnels du droits Section -->
  <section id="professionnels" class="py-16 bg-gradient-to-b from-white to-gray-100" data-aos="fade-up">
    <div class="max-w-7xl mx-auto px-8 md:px-12">
      <!-- Section Header -->
      <div class="text-center mb-12">
        <h2 class="text-3xl font-bold text-gray-800 mb-2">Professionnels du Droit</h2>
        <p class="text-gray-600 max-w-2xl mx-auto">
          Trouvez les experts juridiques dont vous avez besoin pour vous accompagner dans vos démarches
        </p>
      </div>

      <!-- Avocats Section -->
      <div class="mb-16">
        <h3 class="text-2xl font-bold text-gray-800 mb-6 pl-4 border-l-4 border-blue-700">Les Avocats</h3>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Card 1: Trouver un avocat -->
          <div
            class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-2xl hover:scale-105 h-full"
            data-aos="zoom-in"
            data-aos-delay="100"
          >
            <div class="h-3 bg-blue-700"></div>
            <div class="relative h-48 overflow-hidden">
              <img
                src="/img/photo/groupeavocat1.jpeg"
                alt="Les Avocats"
                class="w-full h-full object-cover transform transition-transform duration-700 group-hover:scale-110"
              >
              <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
              <div class="absolute bottom-4 left-4 text-white">
                <div class="bg-blue-700 text-xs font-bold uppercase tracking-wider px-2 py-1 rounded-md inline-block mb-2">
                  Géolocalisation
                </div>
                <h3 class="text-xl font-bold">Trouver un avocat à proximité</h3>
              </div>
            </div>
            <div class="p-6">
              <p class="text-gray-600 mb-4">
                Localisez les cabinets d'avocats les plus proches de chez vous pour obtenir une consultation juridique personnalisée.
              </p>
              <a :href="lawyerGoogleLink" target="_blank" class="w-full mb-4 block">
                <span class="flex group relative w-full bg-white text-green-600 border border-green-600 font-semibold px-2 py-1 rounded-full transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-50 hover:bg-green-600 hover:text-white justify-center items-center">
                  <svg class="w-5 h-5 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  <span class="ml-2">Avec Google Map</span>
                </span>
              </a>
              <a :href="lawyerPagesJaunesLink" target="_blank" class="w-full block">
                <span class="flex group relative w-full bg-white text-yellow-600 border border-yellow-600 font-semibold px-2 py-1 rounded-full transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-opacity-50 hover:bg-yellow-600 hover:text-white justify-center items-center">
                  <svg class="w-5 h-5 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  <span class="ml-2">Avec Pages Jaunes</span>
                </span>
              </a>
            </div>
          </div>

          <!-- Card 2: Avocat.fr -->
          <a href="https://www.avocats.fr/" target="_blank" class="block group">
            <div
              class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-2xl hover:scale-105 h-full"
              data-aos="zoom-in"
              data-aos-delay="200"
            >
              <div class="h-3 bg-blue-700"></div>
              <div class="relative h-48 overflow-hidden bg-blue-50 flex items-center justify-center">
                <img
                  src="/img/logo/logoavocatfr.svg"
                  alt="Avocat.fr"
                  class="h-24 transform transition-transform duration-700 group-hover:scale-110"
                >
                <div class="absolute top-4 right-4">
                  <div class="bg-blue-700 text-white text-xs font-bold uppercase tracking-wider px-2 py-1 rounded-md">
                    Officiel
                  </div>
                </div>
              </div>
              <div class="p-6">
                <div class="flex items-center mb-4">
                  <div class="w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0 transform transition-transform duration-500 group-hover:scale-110 group-hover:bg-blue-200">
                    <i class="fas fa-gavel text-blue-700 text-xl"></i>
                  </div>
                  <div>
                    <h3 class="text-xl font-bold text-gray-800 group-hover:text-blue-700 transition-colors duration-300">
                      Avocat.fr: La Référence
                    </h3>
                    <p class="text-sm text-gray-500">Portail officiel</p>
                  </div>
                </div>
                <p class="text-gray-600 mb-6">
                  Le portail officiel du Conseil National des Barreaux (CNB), l'institution représentant tous les avocats en France.
                </p>
                <div class="flex items-center text-blue-700 font-medium group-hover:text-blue-800 transition-colors duration-300">
                  <span>Visiter le site</span>
                  <i class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-2"></i>
                </div>
              </div>
            </div>
          </a>

          <!-- Card 3: Alexia.fr -->
          <a href="https://www.alexia.fr/" target="_blank" class="block group">
            <div
              class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-2xl hover:scale-105 h-full"
              data-aos="zoom-in"
              data-aos-delay="300"
            >
              <div class="h-3 bg-purple-600"></div>
              <div class="relative h-48 overflow-hidden bg-gray-50 flex items-center justify-center">
                <img
                  src="/img/logo/alexiafr.png"
                  alt="Alexia.fr"
                  class="h-24 transform transition-transform duration-700 group-hover:scale-110"
                >
                <div class="absolute top-4 right-4">
                  <div class="bg-purple-600 text-white text-xs font-bold uppercase tracking-wider px-2 py-1 rounded-md">
                    Plateforme
                  </div>
                </div>
              </div>
              <div class="p-6">
                <div class="flex items-center mb-4">
                  <div class="w-14 h-14 bg-purple-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0 transform transition-transform duration-500 group-hover:scale-110 group-hover:bg-purple-200">
                    <i class="fas fa-search text-purple-600 text-xl"></i>
                  </div>
                  <div>
                    <h3 class="text-xl font-bold text-gray-800 group-hover:text-purple-700 transition-colors duration-300">
                      Alexia.fr
                    </h3>
                    <p class="text-sm text-gray-500">Recherche d'avocats</p>
                  </div>
                </div>
                <p class="text-gray-600 mb-6">
                  Plateforme qui simplifie la recherche d'avocats selon leurs spécialités et leur localisation pour répondre à vos besoins juridiques.
                </p>
                <div class="flex items-center text-purple-600 font-medium group-hover:text-purple-800 transition-colors duration-300">
                  <span>Visiter le site</span>
                  <i class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-2"></i>
                </div>
              </div>
            </div>
          </a>
        </div>
      </div>

      <!-- Notaires et Huissiers Section -->
      <div>
        <h3 class="text-2xl font-bold text-gray-800 mb-6 pl-4 border-l-4 border-blue-700">
          Les Notaires et Huissiers
        </h3>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Card 1: Trouver un notaire -->
          <div
            class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-2xl hover:scale-105 h-full"
            data-aos="zoom-in"
            data-aos-delay="100"
          >
            <div class="h-3 bg-green-700"></div>
            <div class="relative h-48 overflow-hidden">
              <img
                src="/img/photo/bienvenuenotaire.jpg"
                alt="Les Notaires"
                class="w-full h-full object-cover transform transition-transform duration-700 group-hover:scale-110"
              >
              <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
              <div class="absolute bottom-4 left-4 text-white">
                <div class="bg-green-700 text-xs font-bold uppercase tracking-wider px-2 py-1 rounded-md inline-block mb-2">
                  Géolocalisation
                </div>
                <h3 class="text-xl font-bold">Trouver un notaire à proximité</h3>
              </div>
            </div>
            <div class="p-6">
              <p class="text-gray-600 mb-4">
                Localisez les études notariales les plus proches de chez vous pour vos actes authentiques et conseils juridiques.
              </p>
              <a :href="notaryGoogleLink" target="_blank" class="w-full mb-4 block">
                <span class="flex group relative w-full bg-white text-green-600 border border-green-600 font-semibold px-2 py-1 rounded-full transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-50 hover:bg-green-600 hover:text-white justify-center items-center">
                  <svg class="w-5 h-5 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  <span class="ml-2">Avec Google Map</span>
                </span>
              </a>
              <a :href="notaryPagesJaunesLink" target="_blank" class="w-full block">
                <span class="flex group relative w-full bg-white text-yellow-600 border border-yellow-600 font-semibold px-2 py-1 rounded-full transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-opacity-50 hover:bg-yellow-600 hover:text-white justify-center items-center">
                  <svg class="w-5 h-5 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 11111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  <span class="ml-2">Avec Pages Jaunes</span>
                </span>
              </a>
            </div>
          </div>

          <!-- Card 2: Notaires.fr -->
          <a href="https://www.notaires.fr" target="_blank" class="block group">
            <div
              class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-2xl hover:scale-105 h-full"
              data-aos="zoom-in"
              data-aos-delay="200"
            >
              <div class="h-3 bg-green-600"></div>
              <div class="relative h-48 overflow-hidden bg-green-50 flex items-center justify-center">
                <img
                  src="/img/logo/notairesfr.svg"
                  alt="Notaires.fr"
                  class="h-24 transform transition-transform duration-700 group-hover:scale-110"
                >
                <div class="absolute top-4 right-4">
                  <div class="bg-green-600 text-white text-xs font-bold uppercase tracking-wider px-2 py-1 rounded-md">
                    Officiel
                  </div>
                </div>
              </div>
              <div class="p-6">
                <div class="flex items-center mb-4">
                  <div class="w-14 h-14 bg-green-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0 transform transition-transform duration-500 group-hover:scale-110 group-hover:bg-green-200">
                    <i class="fas fa-file-signature text-green-600 text-xl"></i>
                  </div>
                  <div>
                    <h3 class="text-xl font-bold text-gray-800 group-hover:text-green-700 transition-colors duration-300">
                      Notaires.fr
                    </h3>
                    <p class="text-sm text-gray-500">Site officiel</p>
                  </div>
                </div>
                <p class="text-gray-600 mb-6">
                  Le site officiel des Notaires de France offrant un large éventail d'informations et de services liés au notariat.
                </p>
                <div class="flex items-center text-green-600 font-medium group-hover:text-green-800 transition-colors duration-300">
                  <span>Visiter le site</span>
                  <i class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-2"></i>
                </div>
              </div>
            </div>
          </a>

          <!-- Card 3: Devis-Huissier.fr -->
          <a href="https://www.devis-huissier.fr" target="_blank" class="block group">
            <div
              class="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-2xl hover:scale-105 h-full"
              data-aos="zoom-in"
              data-aos-delay="300"
            >
              <div class="h-3 bg-amber-500"></div>
              <div class="relative h-48 overflow-hidden bg-gray-50 flex items-center justify-center">
                <img
                  src="/img/logo/logo-devis-huissier.png"
                  alt="Devis-Huissier.fr"
                  class="h-24 transform transition-transform duration-700 group-hover:scale-110"
                >
                <div class="absolute top-4 right-4">
                  <div class="bg-amber-500 text-white text-xs font-bold uppercase tracking-wider px-2 py-1 rounded-md">
                    Service
                  </div>
                </div>
              </div>
              <div class="p-6">
                <div class="flex items-center mb-4">
                  <div class="w-14 h-14 bg-amber-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0 transform transition-transform duration-500 group-hover:scale-110 group-hover:bg-amber-200">
                    <i class="fas fa-file-invoice-dollar text-amber-500 text-xl"></i>
                  </div>
                  <div>
                    <h3 class="text-xl font-bold text-gray-800 group-hover:text-amber-600 transition-colors duration-300">
                      Devis-Huissiers.fr
                    </h3>
                    <p class="text-sm text-gray-500">Service de devis</p>
                  </div>
                </div>
                <p class="text-gray-600 mb-6">
                  Plateforme permettant de trouver des huissiers de justice situés à proximité et d'obtenir des devis pour vos procédures.
                </p>
                <div class="flex items-center text-amber-500 font-medium group-hover:text-amber-600 transition-colors duration-300">
                  <span>Visiter le site</span>
                  <i class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-2"></i>
                </div>
              </div>
            </div>
          </a>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getLocationByIP } from '@/utils/location';

// Create reactive variables for links
const lawyerGoogleLink = ref('');
const lawyerPagesJaunesLink = ref('');
const notaryGoogleLink = ref('');
const notaryPagesJaunesLink = ref('');

onMounted(async () => {
  try {
    const locationResult = await getLocationByIP();
    let ville = null;
    
    // Check if locationResult is an error object
    if (locationResult && typeof locationResult === 'object' && locationResult.error) {
      console.error('Location error:', locationResult.error);
    } else {
      ville = locationResult;
      console.log('Detected city:', ville);
    }
    
    // Build links with detected city or fallback to generic URLs
    lawyerGoogleLink.value = ville
      ? `https://www.google.com/maps/search/avocat+${encodeURIComponent(ville)}`
      : 'https://www.google.com/maps/search/avocat';
      
    lawyerPagesJaunesLink.value = ville
      ? `https://www.pagesjaunes.fr/carte/recherche?quoiqui=avocat&ou=${encodeURIComponent(ville)}`
      : 'https://www.pagesjaunes.fr/annuaire/chercherlespros?quoiqui=avocat';
      
    notaryGoogleLink.value = ville
      ? `https://www.google.com/maps/search/notaire+${encodeURIComponent(ville)}`
      : 'https://www.google.com/maps/search/notaire';
      
    notaryPagesJaunesLink.value = ville
      ? `https://www.pagesjaunes.fr/carte/recherche?quoiqui=notaire&ou=${encodeURIComponent(ville)}`
      : 'https://www.pagesjaunes.fr/annuaire/chercherlespros?quoiqui=notaire';
  } catch (error) {
    console.error('Unexpected error:', error);
    // Fallback to generic links
    lawyerGoogleLink.value = 'https://www.google.com/maps/search/avocat';
    lawyerPagesJaunesLink.value = 'https://www.pagesjaunes.fr/annuaire/chercherlespros?quoiqui=avocat';
    notaryGoogleLink.value = 'https://www.google.com/maps/search/notaire';
    notaryPagesJaunesLink.value = 'https://www.pagesjaunes.fr/annuaire/chercherlespros?quoiqui=notaire';
  }
});
</script>

<style scoped>
/* Additional styles if needed */
</style>
