<template>
  <!-- Footer -->
  <footer class="bg-gradient-to-b from-gray-100 to-gray-200 text-gray-800" data-aos="fade-up">
    <!-- Main Footer Content -->
    <div class="max-w-7xl mx-auto px-4 py-16 md:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-10">
        <!-- Column 1: About -->
        <div>
          <div class="mb-6">
            <img src="/img/logos/main1.png" alt="QuestionLegale.info" class="h-10 mb-4">
            <h3 class="text-xl font-bold text-gray-800">QuestionLegale.Info</h3>
          </div>
          <p class="text-gray-600 mb-6">
            Votre plateforme d'information juridique utilisant l'intelligence artificielle
            pour répondre à vos questions légales en France.
          </p>
        </div>

        <!-- Column 2: Menu -->
        <div>
          <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">Navigation</h4>
          <ul class="space-y-3">
            <li>
              <a href="#analyse"
                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                Poser une question
              </a>
            </li>
            <li>
              <a href="/Articles/"
                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                Les Articles
              </a>
            </li>
            <li>
              <a href="#comment"
                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                Comment ça marche?
              </a>
            </li>
            <li>
              <a href="#professionnels"
                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                Trouver un Professionnel
              </a>
            </li>
            <li>
              <a href="#utiles"
                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                Sites utiles
              </a>
            </li>
          </ul>
        </div>

        <!-- Column 3: À propos -->
        <div>
          <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">À propos</h4>
          <ul class="space-y-3">
            <li>
              <a href="/mentions.html"
                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                Mentions légales
              </a>
            </li>
            <li>
              <a href="/mentions.html#politique"
                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                Politique de confidentialité
              </a>
            </li>
            <li>
              <a href="/mentions.html#conditions"
                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                Conditions d'utilisation
              </a>
            </li>
            <li>
              <a href="/services.html"
                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                Services destinés aux professionnels
              </a>
            </li>
            <li>
              <a href="/services.html#promotion"
                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                Promotion sur le site web
              </a>
            </li>
          </ul>
        </div>

        <!-- Column 4: Contact -->
        <div>
          <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">Contact</h4>
          <ul class="space-y-3">
            <li class="flex items-start">
              <i class="fas fa-map-marker-alt mt-1 mr-3 text-blue-700"></i>
              <span class="text-gray-600">75003 Paris, France</span>
            </li>
            <li class="flex items-center">
              <i class="fas fa-envelope mr-3 text-blue-700"></i>
              <span class="text-gray-600 hover:text-blue-700 transition-colors duration-300">
                contact (Chez) questionlegale.info
              </span>
            </li>
            <li class="flex items-center">
              <i class="fas fa-clock mr-3 text-blue-700"></i>
              <span class="text-gray-600">Lun - Ven: 9h00 - 18h00</span>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Bottom Bar -->
    <div class="bg-gray-800 text-gray-300 py-6">
      <div class="max-w-7xl mx-auto px-4 md:px-8 flex flex-col md:flex-row justify-between items-center">
        <div class="mb-4 md:mb-0 text-center md:text-left">
          <p>&copy; 2025 QuestionLegale.Info. Tous droits réservés.</p>
        </div>

        <div class="flex items-center">
          <span class="text-sm mr-2">Made with</span>
          <span class="text-red-500 mx-1">❤</span>
          <span class="text-sm">
            by <a href="https://www.ladorure.online"
              class="text-blue-400 hover:text-blue-300 transition-colors duration-300"> LaDorure
            </a>
          </span>
        </div>

        <div class="hidden md:flex items-center mt-4 md:mt-0">
          <a href="mentions.html#mentions" class="text-gray-400 hover:text-white mx-2 transition-colors duration-300">
            Mentions légales
          </a>
          <span class="text-gray-600">|</span>
        </div>
      </div>
    </div>

    <!-- Back to Top Button -->
    <button v-show="showBackToTop" @click="scrollToTop"
      class="fixed bottom-8 right-8 bg-blue-700 text-white p-3 rounded-full shadow-lg hover:bg-blue-800 transition-all duration-300"
      :class="{ 'opacity-0 invisible': !showBackToTop, 'opacity-100 visible': showBackToTop }">
      <i class="fas fa-arrow-up"></i>
    </button>
  </footer>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const showBackToTop = ref(false)

const handleScroll = () => {
  showBackToTop.value = window.pageYOffset > 300
}

const scrollToTop = () => {
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
/* Additional styles if needed */
</style>
