<template>
  <transition name="modal">
    <div v-if="show" class="modal-mask">
      <div class="modal-wrapper">
        <div class="modal-container" :class="modalClass">
          <div class="modal-header">
            <h3>{{ title }}</h3>
          </div>

          <div class="modal-body">
            <slot>
              {{ message }}
            </slot>
          </div>

          <div class="modal-footer">
            <button class="modal-default-button" @click="$emit('modal-closed')">
              OK
            </button>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  action: {
    type: String,
    validator: value => ['login', 'logout'].includes(value),
    required: true
  },
  show: {
    type: Boolean,
    default: false
  }
})

defineEmits(['modal-closed'])

const { title, message, modalClass } = computed(() => {
  if (props.action === 'login') {
    return {
      title: 'Connexion réussie',
      message: 'Vous êtes maintenant connecté',
      modalClass: 'success'
    }
  }
  return {
    title: 'Déconnexion réussie',
    message: 'Vous êtes déconnecté',
      modalClass: 'info'
    }
}).value
</script>

<style scoped>
.modal-mask {
  position: fixed;
  z-index: 9998;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease;
}

.modal-wrapper {
  width: auto; /* Adjust to auto width */
  max-width: 800px; /* Maximum width if needed */
  margin: 0 auto;
  padding: 20px;
}


.modal-container {
  width: 300px;
  padding: 20px 30px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.33);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.modal-header h3 {
  margin-top: 0;
  color: #2c3e50;
}

.modal-body {
  margin: 20px 0;
  flex-grow: 1; /* Allow body to grow and push footer to bottom */
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.modal-default-button {
  padding: 0.75rem 1.5rem;
  background-color: #42b983;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}
.modal-default-button:hover {
  background-color: #3aa876;
}

/* Transition styles */
.modal-enter-from {
  opacity: 0;
}

.modal-leave-to {
  opacity: 0;
}

.modal-enter-from .modal-container,
.modal-leave-to .modal-container {
  transform: scale(1.1);
}

.success {
  border-left: 5px solid #42b983;
}

.info {
  border-left: 5px solid #2196f3;
}
</style>
