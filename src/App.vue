<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup>
import { watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from './stores/auth'

const authStore = useAuthStore()
const router = useRouter()

// Handle navigation based on auth action
watch(() => authStore.authAction, (action) => {
  if (action === 'logout') {
    router.push('/')
    authStore.authAction = null
  } else if (action === 'login') {
    router.push('/restricted')
    authStore.authAction = null
  }
})
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}
</style>
