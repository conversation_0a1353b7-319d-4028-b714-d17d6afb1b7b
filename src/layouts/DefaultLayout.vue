<template>
  <div class="layout-container">
    <!-- Navigation Menu -->
    <NavigationMenu />

    <!-- Main Content -->
    <main class="main-content">
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useAuthStore } from '../stores/auth'
import NavigationMenu from '../components/NavigationMenu.vue'

const authStore = useAuthStore()

onMounted(() => {
  authStore.fetchProfile()
})
</script>

<style scoped>
.layout-container {
  min-height: 100vh;
  background-color: #f3f4f6;
}

.main-content {
  width: 100%;
  min-height: 100vh;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
