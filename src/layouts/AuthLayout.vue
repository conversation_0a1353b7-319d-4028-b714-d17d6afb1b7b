<template>
  <div class="auth-layout">
    <!-- Background image with overlay -->
    <div
      class="absolute inset-0 presentation-card bg-cover bg-center bg-no-repeat"
      data-aos="fade"
      data-aos-duration="1200"
    >
      <div class="absolute inset-0 bg-black bg-opacity-30"></div>
    </div>

    <div class="auth-content">
      <router-view></router-view>
    </div>
  </div>
</template>

<script setup>
</script>

<style scoped>
.auth-layout {
  position: relative;
  min-height: 100vh;
  width: 100%;
  background-color: #f3f4f6;
  overflow: hidden;
}

.presentation-card {
  background-image: url('/img/QuestionLegaleHero.jpeg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.auth-content {
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 2rem;
}

@media (max-width: 768px) {
  .auth-content {
    padding: 1rem;
  }
}
</style>
