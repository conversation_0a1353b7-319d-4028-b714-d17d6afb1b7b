export const getLocationByIP = async () => {
  // Check cache first
  const cachedCity = localStorage.getItem('cachedCity');
  const cacheTimestamp = localStorage.getItem('cacheTimestamp');
  
  if (cachedCity && cacheTimestamp && Date.now() - cacheTimestamp < 3600000) {
    return cachedCity;
  }

  try {
    // Try primary service
    const response1 = await fetch('https://ip-api.com/json/');
    if (response1.ok) {
      const data = await response1.json();
      if (data.status === 'success' && data.city) {
        localStorage.setItem('cachedCity', data.city);
        localStorage.setItem('cacheTimestamp', Date.now());
        return data.city;
      }
    }
    
    // Fallback service
    const response2 = await fetch('https://ipapi.co/json/');
    if (response2.ok) {
      const data = await response2.json();
      if (data.city) {
        localStorage.setItem('cachedCity', data.city);
        localStorage.setItem('cacheTimestamp', Date.now());
        return data.city;
      }
    }
    
    return null;
  } catch (error) {
    console.error('Geolocation error:', error);
    return null;
  }
};