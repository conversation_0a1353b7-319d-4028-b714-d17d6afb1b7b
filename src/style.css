@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap');

:root {
  font-family: 'Roboto', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: #374151;
  background-color: #f3f4f6;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: #f3f4f6;
  color: #374151;
}

#app {
  width: 100%;
  min-height: 100vh;
}

/* Custom styles for sections */
section {
  background-color: rgba(255, 255, 255, 0.1);
  margin-bottom: 20px;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

section:hover {
  opacity: 1;
  transform: translateY(-5px);
}

section > div {
  border-radius: 8px;
  transition: opacity 0.3s ease;
}

section:hover > div {
  opacity: 1;
}

/* Round the corners of buttons with icons */
button i {
  border-radius: 20px;
}

button {
  border-radius: 20px;
}

.presentation-card {
  background-image: url('/img/QuestionLegaleHero.jpeg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.textarea-zoom {
  transition: transform 0.3s ease;
}

.textarea-zoom:focus {
  transform: scale(1.05);
  outline: 2px solid #007bff;
  box-shadow: 0 0 5px #007bff;
}

@media (max-width: 768px) {
  .text-5xl {
    font-size: 2rem;
  }

  #home {
    height: auto !important;
    min-height: 85vh;
  }

  #home .flex-col {
    padding-bottom: 1.5rem;
  }
}
