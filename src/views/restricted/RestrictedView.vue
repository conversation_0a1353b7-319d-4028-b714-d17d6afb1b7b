<template>
  <div class="restricted-container">
    <div class="restricted-content">
      <div class="header">
        <h1>🔒 Zone Restreinte</h1>
        <div class="user-info" v-if="user">
          <span class="user-name">{{ user.email }}</span>
          <span class="access-level">Niveau d'accès : Premium</span>
        </div>
      </div>

      <div class="content-section">
        <h2>Informations Confidentielles</h2>
        
        <div class="api-key-section">
          <h3>Vos Identifiants</h3>
          <div class="api-key-display">
            <div class="credentials">
              <div class="credential-item">
                <span class="credential-label">Email:</span>
                <code>{{ user?.email }}</code>
                <button @click="copyEmail" class="copy-btn" :class="{ emailCopied: emailCopied }">
                  {{ emailCopied ? 'Copié !' : 'Copier' }}
                </button>
              </div>
              <div class="credential-item">
                <span class="credential-label">ID:</span>
                <code>{{ uid }}</code>
                <button @click="copyUid" class="copy-btn" :class="{ copied: uidCopied }">
                  {{ uidCopied ? 'Copié !' : 'Copier' }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="api-key-section">
          <h3>Votre Clé API</h3>
          <div class="api-key-display">
            <code>{{ api_key }}</code>
            <button @click="copyApiKey" class="copy-btn" :class="{ copied: copied }">
              {{ copied ? 'Copié !' : 'Copier' }}
            </button>
          </div>
          <p class="api-key-info">Cette clé API est unique et personnelle. Ne la partagez pas.</p>
        </div>

        <div class="info-card">
          <h3>Contenu Restreint</h3>
          <p>Ceci est une zone sécurisée accessible uniquement aux utilisateurs authentifiés.</p>
          <ul class="feature-list">
            <li>
              <span class="feature-icon">🔐</span>
              <span>Chiffrement de bout en bout</span>
            </li>
            <li>
              <span class="feature-icon">👤</span>
              <span>Tableau de bord personnalisé</span>
            </li>
            <li>
              <span class="feature-icon">📊</span>
              <span>Analyses avancées</span>
            </li>
          </ul>
        </div>

        <div class="actions">
          <button @click="refreshData" :disabled="loading" class="refresh-btn">
            {{ loading ? 'Chargement...' : 'Actualiser les données' }}
          </button>
         
          <router-link to="/dashboard" class="back-link">
            Retour au tableau de bord
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { storeToRefs } from 'pinia'

const router = useRouter()
const authStore = useAuthStore()
const { user, api_key, uid } = storeToRefs(authStore)
const loading = ref(false)
const copied = ref(false)
const emailCopied = ref(false)
const uidCopied = ref(false)

async function refreshData() {
  loading.value = true
  try {
    await authStore.loadUserWithProfile()
  } finally {
    loading.value = false
  }
}

async function copyApiKey() {
  if (!api_key.value) return
  
  try {
    await navigator.clipboard.writeText(api_key.value)
    copied.value = true
    setTimeout(() => {
      copied.value = false
    }, 2000)
  } catch (err) {
    console.error('Failed to copy:', err)
  }
}

async function copyEmail() {
  if (!user.value?.email) return
  
  try {
    await navigator.clipboard.writeText(user.value.email)
    emailCopied.value = true
    setTimeout(() => {
      emailCopied.value = false
    }, 2000)
  } catch (err) {
    console.error('Failed to copy:', err)
  }
}

async function copyUid() {
  if (!uid.value) return
  
  try {
    await navigator.clipboard.writeText(uid.value)
    uidCopied.value = true
    setTimeout(() => {
      uidCopied.value = false
    }, 2000)
  } catch (err) {
    console.error('Failed to copy:', err)
  }
}

onMounted(async () => {
  loading.value = true
  try {
    await authStore.loadUserWithProfile()
    if (!user.value) {
      router.push('/login')
    }
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.restricted-container {
  padding: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.restricted-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.header {
  background: linear-gradient(135deg, #42b983 0%, #3aa876 100%);
  color: white;
  padding: 2rem;
  position: relative;
}

.header h1 {
  margin: 0;
  font-size: 2rem;
}

.user-info {
  margin-top: 1rem;
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  opacity: 0.9;
}

.user-name {
  background: rgba(255, 255, 255, 0.3);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-weight: bold;
}

.user-email {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
}

.access-level {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
}

.content-section {
  padding: 2rem;
}

.api-key-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1rem 0 2rem 0;
}

.api-key-display {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: #fff;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  margin: 1rem 0;
}

.credentials {
  width: 100%;
}

.credential-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.credential-label {
  min-width: 80px;
  font-weight: bold;
  color: #2c3e50;
}

.api-key-display code {
  flex: 1;
  font-family: monospace;
  font-size: 1rem;
  color: #2c3e50;
  overflow-x: auto;
  white-space: nowrap;
  padding: 0.25rem;
}

.copy-btn {
  background: #42b983;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.copy-btn:hover {
  background: #3aa876;
}

.copy-btn.copied,
.copy-btn.emailCopied,
.copy-btn.uidCopied {
  background: #28a745;
}

.api-key-info {
  color: #6c757d;
  font-size: 0.9rem;
  margin: 0.5rem 0 0 0;
}

.info-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1rem 0;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
}

.feature-list li {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e9ecef;
}

.feature-list li:last-child {
  border-bottom: none;
}

.feature-icon {
  font-size: 1.25rem;
}

.actions {
  margin-top: 2rem;
  display: flex;
  gap: 1rem;
  align-items: center;
}

.refresh-btn {
  background: #42b983;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.refresh-btn:hover {
  background: #3aa876;
}

.refresh-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.action-link {
  background: #42b983;
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.action-link:hover {
  background: #3aa876;
}

.back-link {
  color: #6c757d;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.back-link:hover {
  background: #f8f9fa;
}
</style>
