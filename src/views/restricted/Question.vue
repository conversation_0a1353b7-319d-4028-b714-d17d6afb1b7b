<template>
  <div class="question-view">
    <h1>Posez votre question</h1>
    <p>Veuillez saisir votre question complète dans la zone de texte ci-dessous.</p>
    <form @submit.prevent="submitQuestion">
      <textarea
        v-model="questionText"
        name="questionText"
        placeholder="Entrez votre question ici..."
        rows="10"
        cols="80"
        required
      ></textarea>
      <button type="submit">Soumettre la question</button>
    </form>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useQuestionStore } from '../../stores/question.js';

const questionText = ref('');
const router = useRouter();
const questionStore = useQuestionStore();

const submitQuestion = () => {
  questionStore.setQuestion(questionText.value);
  router.push({ name: 'ReponseLegale' });
};
</script>

<style scoped>
.question-view {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
}

h1 {
  text-align: center;
  margin-bottom: 20px;
}

textarea {
  width: 100%;
  padding: 10px;
  margin-bottom: 15px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box; /* Ensures padding doesn't add to width */
  font-family: inherit; /* Use the same font as the rest of the page */
  font-size: 1rem;
}

button {
  display: block;
  width: 100%;
  padding: 10px 15px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}

button:hover {
  background-color: #0056b3;
}
</style>
