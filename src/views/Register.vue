<template>
  <div class="auth-container" data-aos="fade-up" data-aos-duration="800">
    <div class="auth-card bg-white bg-opacity-90 backdrop-blur-sm rounded-xl shadow-xl">
      <!-- Header with logo -->
      <div class="auth-header">
        <div class="flex items-center justify-center mb-6">
          <img src="/img/logos/main1.png" alt="QuestionLegale.Info" class="w-16 h-16 mr-3">
          <div>
            <h2 class="text-2xl font-bold text-gray-800">S'inscrire</h2>
            <p class="text-sm text-gray-600">QuestionLegale.Info</p>
          </div>
        </div>
        <p class="explanation text-gray-700 text-center mb-6">
          Bienvenue sur Question Légale. Pour créer votre compte et accéder à nos services juridiques personnalisés,
          veuillez remplir le formulaire ci-dessous.
        </p>
      </div>

      <form @submit.prevent="handleSubmit" class="auth-form">
        <div class="form-group">
          <label for="name" class="text-gray-700 font-medium">Nom</label>
          <input
            id="name"
            v-model="name"
            type="text"
            required
            placeholder="Entrez votre nom"
            class="auth-input"
          />
        </div>
        <div class="form-group">
          <label for="email" class="text-gray-700 font-medium">E-mail</label>
          <input
            id="email"
            v-model="email"
            type="email"
            required
            placeholder="Entrez votre e-mail"
            class="auth-input"
          />
        </div>
        <div class="form-group">
          <label for="password" class="text-gray-700 font-medium">Mot de passe</label>
          <input
            id="password"
            v-model="password"
            type="password"
            required
            placeholder="Entrez votre mot de passe"
            class="auth-input"
            @input="handlePasswordInput"
            @focus="isPasswordFocused = true"
            @blur="isPasswordFocused = false"
          />
        </div>

        <div class="password-requirements bg-blue-50 border border-blue-200 rounded-lg p-4" :class="{ 'show': isPasswordFocused || password }">
          <h3 class="text-sm font-semibold text-blue-800 mb-3 flex items-center">
            <i class="fas fa-shield-alt mr-2"></i>
            Exigences du mot de passe
          </h3>
          <ul class="space-y-2">
            <li class="flex items-center text-sm" :class="{ 'text-green-600': !passwordErrors.includes('Le mot de passe doit contenir au moins 8 caractères'), 'text-red-600': passwordErrors.includes('Le mot de passe doit contenir au moins 8 caractères') }">
              <i class="fas mr-2" :class="!passwordErrors.includes('Le mot de passe doit contenir au moins 8 caractères') ? 'fa-check-circle text-green-600' : 'fa-times-circle text-red-600'"></i>
              Au moins 8 caractères
            </li>
            <li class="flex items-center text-sm" :class="{ 'text-green-600': !passwordErrors.includes('Le mot de passe doit contenir au moins une lettre majuscule'), 'text-red-600': passwordErrors.includes('Le mot de passe doit contenir au moins une lettre majuscule') }">
              <i class="fas mr-2" :class="!passwordErrors.includes('Le mot de passe doit contenir au moins une lettre majuscule') ? 'fa-check-circle text-green-600' : 'fa-times-circle text-red-600'"></i>
              Une lettre majuscule
            </li>
            <li class="flex items-center text-sm" :class="{ 'text-green-600': !passwordErrors.includes('Le mot de passe doit contenir au moins une lettre minuscule'), 'text-red-600': passwordErrors.includes('Le mot de passe doit contenir au moins une lettre minuscule') }">
              <i class="fas mr-2" :class="!passwordErrors.includes('Le mot de passe doit contenir au moins une lettre minuscule') ? 'fa-check-circle text-green-600' : 'fa-times-circle text-red-600'"></i>
              Une lettre minuscule
            </li>
            <li class="flex items-center text-sm" :class="{ 'text-green-600': !passwordErrors.includes('Le mot de passe doit contenir au moins un chiffre'), 'text-red-600': passwordErrors.includes('Le mot de passe doit contenir au moins un chiffre') }">
              <i class="fas mr-2" :class="!passwordErrors.includes('Le mot de passe doit contenir au moins un chiffre') ? 'fa-check-circle text-green-600' : 'fa-times-circle text-red-600'"></i>
              Un chiffre
            </li>
          </ul>
        </div>

        <div class="form-group">
          <label for="confirmPassword" class="text-gray-700 font-medium">Confirmer le mot de passe</label>
          <input
            id="confirmPassword"
            v-model="confirmPassword"
            type="password"
            required
            placeholder="Confirmez votre mot de passe"
            class="auth-input"
          />
        </div>

        <div v-if="error" class="error-message bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <i class="fas fa-exclamation-circle mr-2"></i>
          {{ error }}
        </div>

        <div v-if="successMessage" class="success-message bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
          <i class="fas fa-check-circle mr-2"></i>
          {{ successMessage }}
        </div>

        <button type="submit" :disabled="loading || passwordErrors.length > 0" class="auth-button primary">
          <i v-if="loading" class="fas fa-spinner fa-spin mr-2"></i>
          <i v-else class="fas fa-user-plus mr-2"></i>
          {{ loading ? 'Chargement...' : "S'inscrire" }}
        </button>

        <p class="auth-link text-center text-gray-600">
          Vous avez déjà un compte ?
          <router-link to="/login" class="text-red-600 hover:text-red-800 font-medium transition-colors">
            Connexion
          </router-link>
        </p>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const name = ref('')
const email = ref('')
const password = ref('')
const confirmPassword = ref('')
const error = ref('')
const loading = ref(false)
const successMessage = ref('')
const passwordErrors = ref([])
const isPasswordFocused = ref(false)

function validatePassword(password) {
  const errors = []
  if (password.length < 8) {
    errors.push('Le mot de passe doit contenir au moins 8 caractères')
  }
  if (!/[A-Z]/.test(password)) {
    errors.push('Le mot de passe doit contenir au moins une lettre majuscule')
  }
  if (!/[a-z]/.test(password)) {
    errors.push('Le mot de passe doit contenir au moins une lettre minuscule')
  }
  if (!/[0-9]/.test(password)) {
    errors.push('Le mot de passe doit contenir au moins un chiffre')
  }
  return errors
}

function handlePasswordInput() {
  passwordErrors.value = validatePassword(password.value)
}

async function handleSubmit() {
  try {
    passwordErrors.value = validatePassword(password.value)
    if (passwordErrors.value.length > 0) {
      return
    }

    if (password.value !== confirmPassword.value) {
      error.value = 'Les mots de passe ne correspondent pas'
      return
    }

    passwordErrors.value = passwordErrors.value.map(e => 
      e.replace('Le mot de passe doit contenir', 'Doit contenir')
    )

    loading.value = true
    error.value = ''
    successMessage.value = '' // Clear success message at the start
    const { error: signUpError } = await authStore.signUp({
      email: email.value,
      password: password.value,
      options: {
        data: {
          name: name.value
        }
      }
    })
    
    if (signUpError) {
      error.value = signUpError.message
      return
    }
    
    successMessage.value = 'Inscription réussie ! Veuillez vous connecter.'
    setTimeout(() => {
      router.push('/login')
    }, 1500)
  } catch (err) {
    error.value = "Une erreur s'est produite lors de l'inscription"
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 1rem;
}

.auth-card {
  padding: 2.5rem;
  width: 100%;
  max-width: 500px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.explanation {
  line-height: 1.6;
  font-size: 0.95rem;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.auth-input {
  padding: 0.875rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 25px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.9);
}

.auth-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.auth-button {
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-button.primary {
  background: linear-gradient(135deg, #dc2626, #ef4444);
  color: white;
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
}

.auth-button.primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #b91c1c, #dc2626);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
}

.auth-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.error-message {
  font-size: 0.875rem;
  display: flex;
  align-items: center;
}

.success-message {
  font-size: 0.875rem;
  display: flex;
  align-items: center;
}

.auth-link {
  margin-top: 1rem;
}

.password-requirements {
  margin: 0.5rem 0;
  transition: all 0.3s ease;
  opacity: 0;
  max-height: 0;
  overflow: hidden;
}

.password-requirements.show {
  opacity: 1;
  max-height: 200px;
}

@media (max-width: 768px) {
  .auth-card {
    padding: 2rem;
    margin: 1rem;
    max-width: 100%;
  }

  .explanation {
    font-size: 0.9rem;
  }
}
</style>
