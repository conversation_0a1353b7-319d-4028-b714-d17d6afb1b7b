<template>
  <div class="auth-container" data-aos="fade-up" data-aos-duration="800">
    <div class="auth-card bg-white bg-opacity-90 backdrop-blur-sm rounded-xl shadow-xl">
      <!-- Header with logo -->
      <div class="auth-header">
        <div class="flex items-center justify-center mb-6">
          <img src="/img/logos/main1.png" alt="QuestionLegale.Info" class="w-16 h-16 mr-3">
          <div>
            <h2 class="text-2xl font-bold text-gray-800">Mettre à jour le mot de passe</h2>
            <p class="text-sm text-gray-600">QuestionLegale.Info</p>
          </div>
        </div>
        <p class="explanation text-gray-700 text-center mb-6">
          Choisissez un nouveau mot de passe sécurisé pour votre compte.
        </p>
      </div>

        <form @submit.prevent="handleUpdatePassword" class="auth-form">
          <div class="form-group">
            <label for="password" class="text-gray-700 font-medium">Nouveau mot de passe</label>
            <input
              id="password"
              v-model="password"
              type="password"
              required
              placeholder="Entrez le nouveau mot de passe"
              class="auth-input"
              @input="handlePasswordInput"
              @focus="isPasswordFocused = true"
              @blur="isPasswordFocused = false"
            />
          </div>
          <div class="form-group">
            <label for="confirmPassword" class="text-gray-700 font-medium">Confirmer le mot de passe</label>
            <input
              id="confirmPassword"
              v-model="confirmPassword"
              type="password"
              required
              placeholder="Confirmez le nouveau mot de passe"
              class="auth-input"
            />
          </div>

          <div v-if="error" class="error-message bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            <i class="fas fa-exclamation-circle mr-2"></i>
            {{ error }}
          </div>

          <div v-if="success" class="success-message bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
            <i class="fas fa-check-circle mr-2"></i>
            {{ success }}
          </div>

          <button type="submit" :disabled="isLoading || passwordErrors.length > 0" class="auth-button primary">
            <i v-if="isLoading" class="fas fa-spinner fa-spin mr-2"></i>
            <i v-else class="fas fa-lock mr-2"></i>
            {{ isLoading ? 'Chargement...' : 'Mettre à jour le mot de passe' }}
          </button>
        </form>
      <div class="password-requirements-panel bg-white bg-opacity-90 backdrop-blur-sm rounded-xl shadow-xl mt-8" :class="{ 'show': isPasswordFocused || password }">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <i class="fas fa-shield-alt mr-2 text-blue-600"></i>
          Exigences du mot de passe
        </h3>
        <ul class="space-y-3">
          <li class="flex items-center" :class="{ 'text-green-600': !passwordErrors.includes('Le mot de passe doit contenir au moins 8 caractères'), 'text-red-600': passwordErrors.includes('Le mot de passe doit contenir au moins 8 caractères') }">
            <i class="fas mr-3" :class="!passwordErrors.includes('Le mot de passe doit contenir au moins 8 caractères') ? 'fa-check-circle text-green-600' : 'fa-times-circle text-red-600'"></i>
            Au moins 8 caractères
          </li>
          <li class="flex items-center" :class="{ 'text-green-600': !passwordErrors.includes('Le mot de passe doit contenir au moins une lettre majuscule'), 'text-red-600': passwordErrors.includes('Le mot de passe doit contenir au moins une lettre majuscule') }">
            <i class="fas mr-3" :class="!passwordErrors.includes('Le mot de passe doit contenir au moins une lettre majuscule') ? 'fa-check-circle text-green-600' : 'fa-times-circle text-red-600'"></i>
            Une lettre majuscule
          </li>
          <li class="flex items-center" :class="{ 'text-green-600': !passwordErrors.includes('Le mot de passe doit contenir au moins une lettre minuscule'), 'text-red-600': passwordErrors.includes('Le mot de passe doit contenir au moins une lettre minuscule') }">
            <i class="fas mr-3" :class="!passwordErrors.includes('Le mot de passe doit contenir au moins une lettre minuscule') ? 'fa-check-circle text-green-600' : 'fa-times-circle text-red-600'"></i>
            Une lettre minuscule
          </li>
          <li class="flex items-center" :class="{ 'text-green-600': !passwordErrors.includes('Le mot de passe doit contenir au moins un chiffre'), 'text-red-600': passwordErrors.includes('Le mot de passe doit contenir au moins un chiffre') }">
            <i class="fas mr-3" :class="!passwordErrors.includes('Le mot de passe doit contenir au moins un chiffre') ? 'fa-check-circle text-green-600' : 'fa-times-circle text-red-600'"></i>
            Un chiffre
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { supabase } from '../lib/supabase'

const router = useRouter()
const password = ref('')
const confirmPassword = ref('')
const error = ref(null)
const success = ref(null)
const isLoading = ref(false)
const passwordErrors = ref([])
const isPasswordFocused = ref(false)

function validatePassword(password) {
  const errors = []
  if (password.length < 8) {
    errors.push('Le mot de passe doit contenir au moins 8 caractères')
  }
  if (!/[A-Z]/.test(password)) {
    errors.push('Le mot de passe doit contenir au moins une lettre majuscule')
  }
  if (!/[a-z]/.test(password)) {
    errors.push('Le mot de passe doit contenir au moins une lettre minuscule')
  }
  if (!/[0-9]/.test(password)) {
    errors.push('Le mot de passe doit contenir au moins un chiffre')
  }
  return errors
}

function handlePasswordInput() {
  passwordErrors.value = validatePassword(password.value)
}

async function handleUpdatePassword() {
  passwordErrors.value = validatePassword(password.value)
  if (passwordErrors.value.length > 0) {
    return
  }

  if (password.value !== confirmPassword.value) {
    error.value = 'Les mots de passe ne correspondent pas'
    return
  }

  isLoading.value = true
  error.value = null
  success.value = null

  try {
    const { error: updateError } = await supabase.auth.updateUser({
      password: password.value
    })

    if (updateError) throw updateError

    success.value = 'Mot de passe mis à jour avec succès'
    setTimeout(() => {
      router.push('/login')
    }, 2000)
  } catch (err) {
    console.error('Update password error:', err)
    error.value = err.message
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 1rem;
}


.auth-card {
  padding: 2.5rem;
  width: 100%;
  max-width: 450px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.explanation {
  line-height: 1.6;
  font-size: 0.95rem;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.auth-input {
  padding: 0.875rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 25px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.9);
}

.auth-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.auth-button {
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-button.primary {
  background: linear-gradient(135deg, #dc2626, #ef4444);
  color: white;
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
}

.auth-button.primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #b91c1c, #dc2626);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
}

.auth-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.error-message {
  font-size: 0.875rem;
  display: flex;
  align-items: center;
}

.success-message {
  font-size: 0.875rem;
  display: flex;
  align-items: center;
}

.password-requirements-panel {
  padding: 2rem;
  width: 100%;
  max-width: 450px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@media (max-width: 768px) {
  .auth-card {
    padding: 2rem;
    margin: 1rem;
    max-width: 100%;
  }

  .password-requirements-panel {
    width: 100%;
    max-width: 100%;
    margin-top: 1rem;
  }

  .explanation {
    font-size: 0.9rem;
  }
}
</style>
