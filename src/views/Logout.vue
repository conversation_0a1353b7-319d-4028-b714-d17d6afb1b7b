<template>
  <div class="logout-container" data-aos="fade-up" data-aos-duration="800">
    <div class="logout-card bg-white bg-opacity-90 backdrop-blur-sm rounded-xl shadow-xl" v-if="loading">
      <!-- Header with logo -->
      <div class="logout-header">
        <div class="flex items-center justify-center mb-6">
          <img src="/img/logos/main1.png" alt="QuestionLegale.Info" class="w-16 h-16 mr-3">
          <div>
            <h2 class="text-2xl font-bold text-gray-800">Déconnexion</h2>
            <p class="text-sm text-gray-600">QuestionLegale.Info</p>
          </div>
        </div>
      </div>

      <!-- Loading content -->
      <div class="logout-content">
        <div class="flex flex-col items-center space-y-4">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin text-4xl text-red-600"></i>
          </div>
          <p class="text-gray-700 text-lg font-medium">Déconnexion en cours...</p>
          <p class="text-gray-600 text-sm text-center">
            Merci d'avoir utilisé QuestionLegale.Info. À bientôt !
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(true)

onMounted(async () => {
  try {
    await authStore.signOut()
  } catch (error) {
    console.error('Erreur lors de la déconnexion:', error)
  } finally {
    loading.value = false;
    await nextTick();
    router.push('/')
  }
})
</script>

<style scoped>
.logout-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 1rem;
}

.logout-card {
  padding: 2.5rem;
  width: 100%;
  max-width: 450px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.logout-header {
  margin-bottom: 2rem;
}

.logout-content {
  padding: 1rem 0;
}

.loading-spinner {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@media (max-width: 768px) {
  .logout-card {
    padding: 2rem;
    margin: 1rem;
  }
}
</style>
