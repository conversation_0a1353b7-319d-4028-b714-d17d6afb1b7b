import { useAuthStore } from '../stores/auth'

// For routes that require authentication
export async function requireAuth() {
  const authStore = useAuthStore()
  
  if (!authStore.isAuthenticated) {
    await authStore.loadUser()
  }
  
  return authStore.isAuthenticated
}

// For public routes that should be accessible only when NOT authenticated
export function redirectIfAuth() {
  const authStore = useAuthStore()
  return !authStore.isAuthenticated
}

// For routes that should be public but track auth state
export function allowPublic() {
  return true
}

// Define route access levels
export const RouteAccess = {
  PUBLIC: 'public',          // Anyone can access
  AUTH_ONLY: 'authenticated',// Only authenticated users
  GUEST_ONLY: 'guest',      // Only non-authenticated users
}
