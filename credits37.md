# Step-by-Step Implementation of a Credit System in Supabase

I'll guide you through implementing a credit system in Supabase that automatically awards 3 credits to new users upon registration and generates unique API keys. Here's a structured approach using a single SQL script you can apply directly to your Supabase instance.

## SQL Implementation Script

```sql
-- Step 1: Extend the auth.users table using a profile table
CREATE TABLE public.user_profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  credits INTEGER DEFAULT 3 NOT NULL,
  api_key TEXT UNIQUE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Step 2: Create a secure function to generate random API keys
CREATE OR REPLACE FUNCTION generate_api_key()
RETURNS TEXT AS $$
DECLARE
  api_key TEXT;
  key_exists BOOLEAN;
BEGIN
  LOOP
    -- Generate a random string for the API key (format: prefix_random-hexadecimal)
    api_key := 'sk_' || encode(gen_random_bytes(16), 'hex');
    
    -- Check if this API key already exists
    SELECT EXISTS (
      SELECT 1 FROM user_profiles WHERE api_key = api_key
    ) INTO key_exists;
    
    -- If the key doesn't exist, exit the loop
    EXIT WHEN NOT key_exists;
  END LOOP;
  
  RETURN api_key;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 3: Create a trigger function to automatically create a profile when a user registers
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (id, api_key)
  VALUES (NEW.id, generate_api_key());
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 4: Create the trigger on the auth.users table
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Step 5: Set up Row Level Security (RLS) for the user_profiles table
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- Step 6: Create policies for access control
-- Users can only view and update their own profile
CREATE POLICY "Users can view their own profile"
  ON public.user_profiles
  FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON public.user_profiles
  FOR UPDATE
  USING (auth.uid() = id);

-- Step 7: Create a function to validate API keys
CREATE OR REPLACE FUNCTION validate_api_key(key TEXT)
RETURNS UUID AS $$
DECLARE
  user_id UUID;
BEGIN
  SELECT id INTO user_id
  FROM public.user_profiles
  WHERE api_key = key;
  
  RETURN user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 8: Create a function to deduct credits
CREATE OR REPLACE FUNCTION deduct_credits(user_uuid UUID, amount INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
  current_credits INTEGER;
BEGIN
  -- Get current credits
  SELECT credits INTO current_credits
  FROM public.user_profiles
  WHERE id = user_uuid;
  
  -- Check if there are enough credits
  IF current_credits >= amount THEN
    -- Deduct credits
    UPDATE public.user_profiles
    SET 
      credits = credits - amount,
      updated_at = now()
    WHERE id = user_uuid;
    
    RETURN TRUE;
  ELSE
    RETURN FALSE;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 9: Create a function to add credits
CREATE OR REPLACE FUNCTION add_credits(user_uuid UUID, amount INTEGER)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE public.user_profiles
  SET 
    credits = credits + amount,
    updated_at = now()
  WHERE id = user_uuid;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 10: Create a function to regenerate API key
CREATE OR REPLACE FUNCTION regenerate_api_key(user_uuid UUID)
RETURNS TEXT AS $$
DECLARE
  new_api_key TEXT;
BEGIN
  SELECT generate_api_key() INTO new_api_key;
  
  UPDATE public.user_profiles
  SET 
    api_key = new_api_key,
    updated_at = now()
  WHERE id = user_uuid;
  
  RETURN new_api_key;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Explanation

1. **Table Structure**: Creates a `user_profiles` table linked to Supabase's auth system with fields for credits and API keys.

2. **API Key Generation**: Implements a secure function to generate unique API keys with a "sk_" prefix followed by random hexadecimal characters.

3. **Automatic Profile Creation**: Sets up a trigger to automatically create a profile with 3 credits and a unique API key whenever a new user registers.

4. **Security**: Implements Row Level Security (RLS) to ensure users can only access their own data.

5. **Credit Management**: Provides functions to add and deduct credits safely.

6. **API Key Validation**: Includes a function to validate API keys and retrieve the associated user.

7. **API Key Regeneration**: Allows users to regenerate their API key if needed.

## Usage in Your Application

Once you apply this script to your Supabase instance, you can:

1. Access user credits through the user_profiles table
2. Validate API keys with the `validate_api_key` function
3. Manage credits with the `add_credits` and `deduct_credits` functions
4. Allow users to regenerate their API key with `regenerate_api_key`

This implementation provides a solid foundation for a credit-based system that you can expand upon for various features in your application.