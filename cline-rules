
# Cline Project Rules
# These rules help the AI align with the coding style, workflow, and expectations in this Cline project.

[general]
use_project_rules = true
verify_context = true
follow_plan = true

[rules]

# Base Principles
1 = Always refer to `.cline-rules` for project context. Avoid assumptions or coding beyond these rules.
2 = Confirm any unclear info with the user before proceeding.
3 = Follow `task-plan.md` strictly when implementing features. <PERSON> completed steps with `- [x]` and a short note.

# Workflow
4 = Apply changes one file at a time. Allow user review per file.
5 = Understand the full context before modifying or generating content.
6 = Avoid suggesting major refactors or rewrites unless requested.
7 = Never summarize code unless the user explicitly asks.
8 = Don't re-document unchanged code or generate boilerplate unless necessary.

# Code Integrity
9 = Preserve working code and user changes unless corrections are essential.
10 = Reuse existing code blocks when functional.
11 = Avoid introducing new patterns or abstractions unless asked.
12 = Keep changes minimal, efficient, and relevant to the task.

# Communication
13 = Explain why a change is made unless it's completely obvious.
14 = Keep code and comments concise and purposeful.
15 = Use clear, explicit names for variables and functions.
16 = Keep external dependencies minimal and justify their use.

# Quality & Safety
17 = Maintain consistent inputs/outputs and code structure.
18 = Refactor only if it improves clarity or reusability.
19 = Consider edge cases. Add error handling where needed.
20 = Add assertions or checks to validate assumptions in the code.

# Compatibility
21 = Ensure compatibility with the current CLI environment and dependencies.
22 = Avoid features incompatible with the target system version.
