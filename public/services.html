<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuestionLegale.info: Trouvez des réponses juridiques en France</title>
    <meta name="description"
        content="QuestionLegale.info repond a vos questions d'ordre legale ou juridique et vous oriente vers le professinnel idoine pour votre situation.">
    <meta name="author" content="QuestionLegale.info">
    <meta name="keywords"
        content="justice France, démarches juridiques, tribunal, droits, procédures judiciaires,
         QuestionLegale, Reponses Juridiques, Avocats, Huissiers, Notaires, Questions Legales, Reponses Juridiques, Avocats, Huissiers, Notaires">

    <meta property="og:title" content="QuestionLegale.info: Trouvez des réponses juridiques en France">
    <meta property="og:description"
        content="QuestionLegale.info repond a vos questions d'ordre legale ou juridique et vous oriente vers le professinnel idoine pour votre situation.">
    <meta property="og:image" content="img/logo.png">
    <meta property="og:url" content="https://www.questionlegale.info">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="fr_FR">
    <meta property="og:site_name" content="QuestionLegale.info">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <script src="https://cdn.tailwindcss.com"></script>

    <link rel="stylesheet" href="https://unpkg.com/@fortawesome/fontawesome-free@6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Local JS (detecteur francais, localisation avocat naotaire) -->
    <script src="js/main.js"></script>

    <!-- Basic Favicon -->
    <link rel="icon" href="img/favicons/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="img/favicons/favicon.ico" type="image/x-icon">

    <!-- Modern Browsers -->
    <link rel="icon" type="image/png" sizes="32x32" href="img/favicons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="img/favicons/favicon-16x16.png">

    <!-- Apple Devices -->
    <link rel="apple-touch-icon" sizes="180x180" href="img/favicons/apple-touch-icon.png">
    <link rel="mask-icon" href="img/favicons/safari-pinned-tab.svg" color="#5bbad5">

    <!-- Android Chrome -->
    <link rel="manifest" href="img/favicons/site.webmanifest">
    <meta name="theme-color" content="#ffffff">

    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="msapplication-config" content="img/favicons/rowserconfig.xml">
    <meta name="msapplication-TileImage" content="img/favicons/mstile-144x144.png">

    <!-- SVG Favicon (Optional) -->
    <link rel="icon" type="image/svg+xml" href="img/favicons/favicon.svg">

    <!-- Theme Colors for Different Platforms -->
    <meta name="theme-color" content="#ffffff" media="(prefers-color-scheme: light)">
    <meta name="theme-color" content="#000000" media="(prefers-color-scheme: dark)">
    <meta itemprop="image" content="img/favicons/logo.png">


    <style>
        /* Custom styles */


        section {
            background-color: rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        section:hover {
            opacity: 1;
            transform: translateY(-5px);
        }

        section>div {
            /*background-color: rgba(255, 255, 255, 0.9);*/
            border-radius: 8px;
            transition: opacity 0.3s ease;
        }

        section:hover>div {
            opacity: 1;
        }

        /* Position "signin" and "signup" buttons to the top right */

        /* Round the corners of buttons with icons */
        button i {
            border-radius: 20px;
            /* Adjust the radius as needed */
        }

        button {
            border-radius: 20px;
            /* Adjust the radius as needed */
        }


        .presentation-card {
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }

        .textarea-zoom {
            transition: transform 0.3s ease;
        }

        .textarea-zoom:focus {
            transform: scale(1.05);
        }

        .text-5xl {
            font-size: 2.5rem;
            /* Default size */
        }

        .textarea-zoom:focus {
            outline: 2px solid #007bff;
            box-shadow: 0 0 5px #007bff;
        }

        @media (max-width: 768px) {
            .text-5xl {
                font-size: 2rem;
                /* Smaller size for smaller screens */
            }
        }

        @media (max-width: 768px) {
            #home {
                height: auto !important;
                min-height: 85vh;
            }

            #home .flex-col {
                padding-bottom: 1.5rem;
            }
        }
    </style>
</head>


<body class="bg-gray-300 text-gray-800">

  <!-- Sticky Menu -->
    <nav
        class="fixed top-0 md:top-2 left-0 md:left-1/2 transform md:-translate-x-1/2 bg-white opacity-90 md:rounded-full shadow-xl px-4 md:px-6 py-1.5 flex items-center justify-between w-full md:w-[90%] md:max-w-5xl z-50">
        <!-- Left: Icon and Title -->
        <a href="../index.html" tooltip="Question Legale">
            <div class="flex items-center space-x-2">
                <!-- Icon: replace emoji with SVG or image if needed -->
                <img src="../img/logos/main1.png" alt="Justice.fr" class="w-6 h-6">
                <div class="font-bold text-sm md:text-lg">QuestionLegale.Info</div>

            </div>
        </a>

        <!-- Mobile Section: Burger and Buttons -->
        <div class="flex items-center md:hidden space-x-3">
            <!-- Buttons: visible on mobile -->
            <a href=/login">
                <button id="mobileSignIn" type="button" class="px-2 py-0.5 border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition text-xs
                whitespace-nowrap">
                    Se Connecter
                </button>
            </a>
            <!-- Burger menu button -->
            <button id="burgerBtn" type="button" class="focus:outline-none p-0.5" aria-label="Toggle menu">
                <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
            </button>
        </div>

        <!-- Center: Menu Items (Desktop) -->
        <div id="menuItems" class="hidden md:flex space-x-6 mx-auto w-full max-w-md justify-center">
            <a href="../index.html#analyse" class="text-gray-700 hover:text-blue-500 font-medium text-sm">Question?</a>
            <a href="/Articles/index.htm" class="text-gray-700 hover:text-blue-500 font-medium text-sm">Articles</a>
            <a href="../index.html#references"
                class="text-gray-700 hover:text-blue-500 font-medium text-sm">References</a>
        </div>

        <!-- Right: Sign In and Sign Up Buttons (Desktop) -->
        <div class="hidden md:flex items-center space-x-2">
            <a href="/login">
                <button id="signInBtn" type="button"
                    class="px-3 py-1 border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition text-sm whitespace-nowrap">
                    Se Connecter
                </button>
            </a>
        </div>
    </nav>

    <!-- Mobile Menu: Only for menu items (dropdown) -->
    <div id="mobileMenu"
        class="fixed top-[45px] left-0 w-full bg-white shadow-md rounded-b-lg p-4 md:hidden hidden z-40">
        <div class="flex flex-col space-y-3">
            <a href="/#analyse" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Question?</a>
            <a href="index.htm" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Articles</a>
            <a href="/#references" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">References</a>
            <a href="/services.html" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Services</a>
        </div>
    </div>
    
    <!-- Presentation -->
    <section id="home" class="relative mb-12 py-16">
        <!-- Background image with overlay -->
        <div class="absolute inset-0 presentation-card bg-cover bg-right-top bg-no-repeat" data-aos="fade"
            data-aos-duration="1200">
            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
        </div>

        <div class="relative z-10 flex flex-col md:flex-row pt-16 md:pt-14">
            <!-- Contenu de la page -->
            <div class="w-full flex flex-col justify-start px-8 md:px-12 mt-4 md:mt-8" data-aos="fade-down"
                data-aos-duration="800">
                <div class="bg-white bg-opacity-75 backdrop-blur-sm rounded-xl shadow-xl p-6 md:p-8">
                    

                    <h1 class="text-3xl font-extrabold sm:text-4xl text-black mb-4 mt-8">Services pour les Professionnels du Droit</h1>

                    <p class="mb-4">Dans un paysage juridique en constante évolution, l'efficacité et la visibilité en ligne sont devenues des piliers essentiels pour les professionnels du droit. Chez QuestionLegale.info, nous comprenons les défis uniques auxquels sont confrontés les avocats, notaires, huissiers de justice et autres experts juridiques. C'est pourquoi nous avons développé une gamme de services logiciels et de solutions de promotion spécifiquement conçues pour optimiser votre pratique et renforcer votre présence numérique.</p>

                    <h3 class="text-xl font-bold text-black mb-2">Logiciels et Outils pour une Pratique Juridique Optimisée</h3>
                    <p class="mb-4">Nos solutions logicielles sont pensées pour simplifier et automatiser les tâches quotidiennes, vous permettant de vous concentrer sur l'essentiel : le conseil et la défense de vos clients. Chaque outil est conçu pour s'intégrer harmonieusement à votre flux de travail existant, garantissant une transition fluide et une amélioration immédiate de votre productivité.</p>

                    <h3 class="text-lg font-semibold text-black mb-2">Automatisation Documentaire</h3>
                    <p class="mb-4">La rédaction de documents juridiques est souvent répétitive et chronophage. Nos outils d'automatisation documentaire vous permettent de générer rapidement des contrats, des actes, des courriers et d'autres documents standards à partir de modèles personnalisables. Vous gagnez un temps précieux, tout en garantissant la conformité et la cohérence de vos productions. Les modèles peuvent être adaptés à vos spécificités, intégrant des clauses types et des champs dynamiques pour une personnalisation maximale.</p>
                   
                   <p></p>
                    <h1 id="promotion" class="text-3xl font-extrabold sm:text-4xl text-black mb-4 mt-8">Pourquoi et Comment Promouvoir Votre Activité sur QuestionLegale.info ?</h1>
                    <p class="mb-4">QuestionLegale.info est une plateforme de référence pour les particuliers et les entreprises en quête d'informations juridiques fiables et de professionnels qualifiés. Intégrer notre annuaire et utiliser nos espaces publicitaires, c'est choisir une stratégie de visibilité ciblée et efficace.</p>

                    <h3 class="text-lg font-semibold text-black mb-2">Accès à une Audience Qualifiée</h3>
                    <p class="mb-4">Notre site attire quotidiennement des milliers d'utilisateurs ayant des besoins juridiques concrets. En vous inscrivant sur QuestionLegale.info, vous bénéficiez d'une exposition directe auprès d'une audience déjà pré-qualifiée, activement à la recherche de services juridiques. Cela représente une opportunité unique de générer des contacts pertinents et de développer votre clientèle.</p>

                    <h3 class="text-lg font-semibold text-black mb-2">Renforcement de Votre Notoriété et Crédibilité</h3>
                    <p class="mb-4">Être présent sur une plateforme reconnue comme QuestionLegale.info renforce votre crédibilité et votre notoriété. Les utilisateurs perçoivent notre site comme une source fiable, et votre présence y est un gage de professionnalisme. Vous pouvez y présenter votre expertise, vos domaines de spécialisation, et vos références, construisant ainsi une image de marque solide et digne de confiance.</p>

                    <h3 class="text-lg font-semibold text-black mb-2">Options de Promotion Flexibles</h3>
                    <p class="mb-4">Nous proposons diverses options pour maximiser votre visibilité :</p>
                    <ul class="list-disc list-inside mb-4 pl-4">
                        <li><strong>Fiche Profil Détaillée :</strong> Créez une fiche complète présentant votre cabinet, vos domaines d'intervention, vos coordonnées, et même des témoignages clients. Cette fiche est optimisée pour le référencement naturel (SEO) afin d'apparaître en bonne position dans les résultats de recherche.</li>
                        <li><strong>Articles Sponsorisés :</strong> Partagez votre expertise en rédigeant des articles sur des sujets juridiques pertinents. Ces articles, publiés sur notre blog, vous positionnent comme une autorité dans votre domaine et génèrent du trafic qualifié vers votre profil.</li>
                        <li><strong>Bannières Publicitaires :</strong> Affichez des bannières stratégiquement placées sur le site pour une visibilité maximale. Nos formats publicitaires sont conçus pour attirer l'attention et inciter au clic, augmentant ainsi le nombre de visites sur votre profil ou votre site web.</li>
                        <li><strong>Mise en Avant dans les Résultats de Recherche :</strong> Optez pour un positionnement privilégié dans les résultats de recherche de notre annuaire, assurant que votre profil soit parmi les premiers consultés par les utilisateurs.</li>
                    </ul>

                    <h3 class="text-lg font-semibold text-black mb-2">Processus Simple et Accompagnement Personnalisé</h3>
                    <p class="mb-4">Le processus d'inscription et de mise en place de votre promotion est simple et intuitif. Notre équipe est à votre disposition pour vous accompagner à chaque étape, de la création de votre profil à la mise en œuvre de votre stratégie de visibilité. Nous vous aidons à choisir les options les plus adaptées à vos objectifs et à optimiser votre présence pour un retour sur investissement maximal.</p>

                    <p class="mb-4">En choisissant QuestionLegale.info, vous optez pour un partenaire dédié à votre succès. Nos services logiciels et nos solutions de promotion sont des leviers puissants pour moderniser votre pratique, attirer de nouveaux clients et consolider votre positionnement sur le marché juridique.</p>
                </div>
            </div>
        </div>
    </section>


   
    <!-- Footer -->
    <footer class="bg-gradient-to-b from-gray-100 to-gray-200 text-gray-800" data-aos="fade-up">

        <!-- Main Footer Content -->
        <div class="max-w-7xl mx-auto px-4 py-16 md:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-10">
                <!-- Column 1: About -->
                <div>
                    <div class="mb-6">
                        <img src="img/logos/main1.png" alt="QuestionLegale.info" class="h-10 mb-4">
                        <h3 class="text-xl font-bold text-gray-800">QuestionLegale.Info</h3>
                    </div>
                    <p class="text-gray-600 mb-6">Votre plateforme d'information juridique utilisant l'intelligence
                        artificielle pour répondre à vos questions légales en France.</p>


                </div>

                <!-- Column 2: Menu -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">Navigation</h4>
                    <ul class="space-y-3">
                        <li>
                            <a href="index.html#analyse"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Poser une question
                            </a>
                        </li>
                        <li>
                            <a href="Articles/index.htm"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Les Articles
                            </a>
                        </li>
                        <li>
                            <a href="index.html#comment"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Comment ça marche?
                            </a>
                        </li>
                        <li>
                            <a href="index.html#professionnels"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Trouver un Professionnel
                            </a>
                        </li>
                        <li>
                            <a href="index.html#utiles"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Sites utiles
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Column 3: À propos -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">À propos</h4>
                    <ul class="space-y-3">
                        <li>
                            <a href="mentions.html"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Mentions légales
                            </a>
                        </li>
                        <li>
                            <a href="mentions.html#politique"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Politique de confidentialité
                            </a>
                        </li>
                        <li>
                            <a href="mentions.html#conditions"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Conditions d'utilisation
                            </a>
                        </li>
                        <li>
                            <a href="services.html"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Services destinés aux professionnels
                            </a>
                        </li>
                        <li>
                            <a href="services.html#promotion"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Promotion sur le site web
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Column 4: Contact -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">Contact</h4>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <i class="fas fa-map-marker-alt mt-1 mr-3 text-blue-700"></i>
                            <span class="text-gray-600">75003 Paris, France</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-3 text-blue-700"></i>
                            <span
                                class="text-gray-600 hover:text-blue-700 transition-colors duration-300">contact (Chez) questionlegale.info</span>
                        </li>
                        <!-- <li class="flex items-center">
                            <i class="fas fa-phone-alt mr-3 text-blue-700"></i>
                            <a href="tel:+33123456789"
                                class="text-gray-600 hover:text-blue-700 transition-colors duration-300">+33 1 23 45 67
                                89</a>
                        </li> -->
                        <li class="flex items-center">
                            <i class="fas fa-clock mr-3 text-blue-700"></i>
                            <span class="text-gray-600">Lun - Ven: 9h00 - 18h00</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Bottom Bar -->
        <div class="bg-gray-800 text-gray-300 py-6">
            <div class="max-w-7xl mx-auto px-4 md:px-8 flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0 text-center md:text-left">
                    <p>&copy; 2025 QuestionLegale.Info. Tous droits réservés.</p>
                </div>

                <div class="flex items-center">
                    <span class="text-sm mr-2">Made with</span>
                    <span class="text-red-500 mx-1">❤</span>
                    <span class="text-sm">by <a href="#"
                            class="text-blue-400 hover:text-blue-300 transition-colors duration-300">LaDorure
                            Team</a></span>
                </div>

                <div class="hidden md:flex items-center mt-4 md:mt-0">
                    <a href="mentions.html" class="text-gray-400 hover:text-white mx-2 transition-colors duration-300">Mentions
                        légales</a>
                    <span class="text-gray-600">|<!-- Bottom Bar -->
    <div class="bg-gray-800 text-gray-300 py-6">
      <div class="max-w-7xl mx-auto px-4 md:px-8 flex flex-col md:flex-row justify-between items-center">
        <div class="mb-4 md:mb-0 text-center md:text-left">
          <p>&copy; 2025 QuestionLegale.Info. Tous droits réservés.</p>
        </div>

        <div class="flex items-center">
          <span class="text-sm mr-2">Made with</span>
          <span class="text-red-500 mx-1">❤</span>
          <span class="text-sm">
            by <a href="https://www.ladorure.online"
              class="text-blue-400 hover:text-blue-300 transition-colors duration-300"> LaDorure
            </a>
          </span>
        </div>

        <div class="hidden md:flex items-center mt-4 md:mt-0">
          <a href="mentions.html#mentions" class="text-gray-400 hover:text-white mx-2 transition-colors duration-300">
            Mentions légales
          </a>
          <span class="text-gray-600">|</span>
        </div>
      </div>
    </div>

        <!-- Back to Top Button -->
        <a href="#" id="back-to-top"
            class="fixed bottom-8 right-8 bg-blue-700 text-white p-3 rounded-full shadow-lg hover:bg-blue-800 transition-colors duration-300 opacity-0 invisible">
            <i class="fas fa-arrow-up"></i>
        </a>

        <script>
            // Back to top button functionality
            const backToTopButton = document.getElementById('back-to-top');

            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    backToTopButton.classList.remove('opacity-0', 'invisible');
                    backToTopButton.classList.add('opacity-100', 'visible');
                } else {
                    backToTopButton.classList.remove('opacity-100', 'visible');
                    backToTopButton.classList.add('opacity-0', 'invisible');
                }
            });

            backToTopButton.addEventListener('click', (e) => {
                e.preventDefault();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        </script>
    </footer>

    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="js/menu.js"></script>
    <script>
        // Enhanced AOS initialization with more options
        AOS.init({
            easing: 'ease-out-cubic',
            duration: 1000,
            once: false,
            mirror: true,
            anchorPlacement: 'top-bottom',
            offset: 120,
            delay: 100,
            disable: 'mobile'
        });

        // Additional interactive effects
        document.querySelectorAll('.hover-zoom').forEach(link => {
            link.addEventListener('mouseenter', (e) => {
                e.target.style.transition = 'transform 0.3s ease, color 0.3s ease';
            });
        });

        // Refresh AOS on window resize for better responsiveness
        window.addEventListener('resize', function () {
            AOS.refresh();
        });

        function toggleFaq(id) {
            const content = document.getElementById(id);
            const icon = document.getElementById(id + '-icon');

            // Toggle content visibility
            content.classList.toggle('hidden');

            // Rotate icon when expanded
            if (content.classList.contains('hidden')) {
                icon.classList.remove('rotate-180');
            } else {
                icon.classList.add('rotate-180');
            }
        }
    </script>

    <script>

        //Localisation google
        function LocAvocatG() {
            const button = document.getElementById('locationButtonG');
            alert('function click');
            // Disable button while processing
            button.disabled = true;
            button.classList.add('opacity-75', 'cursor-not-allowed');


            navigator.geolocation.getCurrentPosition(
                function (position) {
                    const latitude = position.coords.latitude;
                    const longitude = position.coords.longitude;


                    alert('function click2');
                    // Create Google Maps URL with current location
                    //const mapsUrl = `https://www.google.com/maps/search/avocat/@${latitude},${longitude},14z`;
                    window.open(`https://www.google.com/maps/search/avocat/@${latitude},${longitude},14z`, "_blank", "noopener,noreferrer");
                    //window.open("https://example.com", "_blank", "noopener,noreferrer");
                },
                function (error) {
                    button.disabled = false;
                    button.classList.remove('opacity-75', 'cursor-not-allowed');

                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 0
                }
            );
            button.disabled = false;
        }

        function LocAvocatPJ() {
            const button = document.getElementById('locationButtonG');
            alert('function click');
            // Disable button while processing
            button.disabled = true;
            button.classList.add('opacity-75', 'cursor-not-allowed');

            window.open(`https://www.alexia.fr`, "_blank", "noopener,noreferrer");

            navigator.geolocation.getCurrentPosition(
                function (position) {
                    const latitude = position.coords.latitude;
                    const longitude = position.coords.longitude;

                    //TODO Find city or zip with lat,long


                    // Create Google Maps URL with current location
                    //https://www.pagesjaunes.fr/annuaire/chercherlespros?quoiqui=avocat&ou=75
                    window.open(`https://www.google.com/maps/search/avocat/@${latitude},${longitude},14z`, "_blank", "noopener,noreferrer");
                    //window.open("https://example.com", "_blank", "noopener,noreferrer");
                },
                function (error) {
                    button.disabled = false;
                    button.classList.remove('opacity-75', 'cursor-not-allowed');

                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 0
                }
            );
            button.disabled = false;

        }

        initStatic();


    </script>


</body>

</html>
