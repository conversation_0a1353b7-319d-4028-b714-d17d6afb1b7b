// Mobile menu functionality
document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle function
    window.toggleMobileMenu = function() {
        const mobileMenu = document.getElementById('mobileMenu');
        mobileMenu.classList.toggle('hidden');
    }
    
    // Add floating menu behavior for mobile
    const handleScroll = () => {
        const nav = document.querySelector('nav');
        if (window.scrollY > 20) {
            nav.classList.add('shadow-lg');
            nav.classList.add('bg-white');
            nav.classList.remove('bg-opacity-90');
        } else {
            nav.classList.remove('shadow-lg');
            nav.classList.add('bg-opacity-90');
        }
    };
    
    window.addEventListener('scroll', handleScroll);

    // Ensure burger button works
    const burgerBtn = document.getElementById('burgerBtn');
    if (burgerBtn) {
        burgerBtn.addEventListener('click', toggleMobileMenu);
    }

    // Add click event listeners for mobile menu items
    document.querySelectorAll('#mobileMenu a').forEach(link => {
        link.addEventListener('click', toggleMobileMenu);
    });

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(event) {
        const mobileMenu = document.getElementById('mobileMenu');
        const burgerBtn = document.getElementById('burgerBtn');
        
        // If menu is open and click is outside menu and not on burger button
        if (!mobileMenu.classList.contains('hidden') && 
            !mobileMenu.contains(event.target) && 
            !burgerBtn.contains(event.target)) {
            mobileMenu.classList.add('hidden');
        }
    });

    // Handle window resize - close mobile menu on desktop view
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 768) { // 768px is the md breakpoint in Tailwind
            const mobileMenu = document.getElementById('mobileMenu');
            if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.add('hidden');
            }
        }
    });
});