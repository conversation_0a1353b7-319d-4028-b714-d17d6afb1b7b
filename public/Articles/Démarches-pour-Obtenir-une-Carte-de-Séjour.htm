<!DOCTYPE html>
<html lang="fr" class="h-full">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Carte de Séjour en France : Guide Complet des Démarches et Documents - QuestionLegale.info</title>
    <meta name="description"
        content="Obtenez votre carte de séjour en France : découvrez les démarches essentielles, critères d'éligibilité, documents requis et conseils pratiques pour votre demande de titre de séjour. Guide détaillé par QuestionLegale.info.">
    <meta name="author" content="QuestionLegale.info">
    <meta name="keywords"
        content="carte de séjour, titre de séjour, démarches carte de séjour, obtenir carte de séjour, immigration France, visa France, regroupement familial, étudiant France, salarié France, documents carte de séjour, préfecture, QuestionLegale.info, droit des étrangers">

    <meta property="og:title" content="Carte de Séjour en France : Guide Complet des Démarches et Documents - QuestionLegale.info">
    <meta property="og:description"
        content="Obtenez votre carte de séjour en France : découvrez les démarches essentielles, critères d'éligibilité, documents requis et conseils pratiques pour votre demande de titre de séjour. Guide détaillé par QuestionLegale.info.">
    <meta property="og:image" content="img/logo.png">
    <meta property="og:url" content="https://www.questionlegale.info/Articles/Démarches-pour-Obtenir-une-Carte-de-Séjour.html">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="fr_FR">
    <meta property="og:site_name" content="QuestionLegale.info">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <script src="https://cdn.tailwindcss.com"></script>

    <link rel="stylesheet" href="https://unpkg.com/@fortawesome/fontawesome-free@6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Local JS (detecteur francais, localisation avocat naotaire) -->
    <script src="../js/menu.js"></script>

    <!-- Basic Favicon -->
    <link rel="icon" href="img/favicons/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="img/favicons/favicon.ico" type="image/x-icon">

    <!-- Modern Browsers -->
    <link rel="icon" type="image/png" sizes="32x32" href="img/favicons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="img/favicons/favicon-16x16.png">

    <!-- Apple Devices -->
    <link rel="apple-touch-icon" sizes="180x180" href="img/favicons/apple-touch-icon.png">
    <link rel="mask-icon" href="img/favicons/safari-pinned-tab.svg" color="#5bbad5">

    <!-- Android Chrome -->
    <link rel="manifest" href="img/favicons/site.webmanifest">
    <meta name="theme-color" content="#ffffff">

    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="msapplication-config" content="img/favicons/browserconfig.xml">
    <meta name="msapplication-TileImage" content="img/favicons/mstile-144x144.png">

    <!-- SVG Favicon (Optional) -->
    <link rel="icon" type="image/svg+xml" href="img/favicons/favicon.svg">

    <!-- Theme Colors for Different Platforms -->
    <meta name="theme-color" content="#ffffff" media="(prefers-color-scheme: light)">
    <meta name="theme-color" content="#000000" media="(prefers-color-scheme: dark)">
    <meta itemprop="image" content="img/favicons/logo.png">


    <style>
        /* ========================================
   Enhanced Blog Content Styles
   ======================================== */

        /* Modern CSS Custom Properties for Consistent Theming */
        :root {
            --blog-primary-color: #1a1a2e;
            --blog-secondary-color: #4a5568;
            --blog-accent-color: #667eea;
            --blog-text-color: #2d3748;
            --blog-light-bg: #f8fafc;
            --blog-hover-bg: #edf2f7;
            --blog-border-color: #e2e8f0;
            --blog-spacing-xs: 0.5rem;
            --blog-spacing-sm: 1rem;
            --blog-spacing-md: 1.5rem;
            --blog-spacing-lg: 2rem;
            --blog-spacing-xl: 2.5rem;
            --blog-border-radius: 0.5rem;
            --blog-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Base Blog Content Container */
        .blog-content {
            max-width: 65ch;
            /* Optimal reading width */
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--blog-text-color);
            line-height: 1.7;
        }

        /* Enhanced Image Styles */
        .blog-content img {
            max-width: 100%;
            height: auto;
            margin: var(--blog-spacing-xl) 0;
            border-radius: var(--blog-border-radius);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: var(--blog-transition);
        }

        .blog-content img:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        /* Improved Typography */
        .blog-content p {
            margin-bottom: var(--blog-spacing-md);
            line-height: 1.2;
            font-size: 1.1rem;
            font-weight: 400;
        }

        /* Modern Heading Styles */
        .blog-content h2 {
            font-size: clamp(1.75rem, 4vw, 2.5rem);
            /* Responsive sizing */
            font-weight: 800;
            margin: var(--blog-spacing-xl) 0 var(--blog-spacing-md);
            color: var(--blog-primary-color);
            letter-spacing: -0.025em;
            line-height: 1.2;
            position: relative;
            padding-bottom: var(--blog-spacing-sm);
        }

        /* Modern gradient underline instead of solid border */
        .blog-content h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, var(--blog-accent-color), transparent);
            border-radius: 2px;
            transition: var(--blog-transition);
        }

        .blog-content h2:hover::after {
            width: 100px;
        }

        .blog-content h3 {
            font-size: clamp(1.25rem, 3vw, 1.75rem);
            font-weight: 600;
            margin: var(--blog-spacing-lg) 0 0.75rem;
            color: var(--blog-secondary-color);
            letter-spacing: -0.01em;
            line-height: 1.1;
        }

        /* Clean and Professional List Styles */
        .blog-content ul {
            list-style: none;
            padding-left: 0;
            margin: var(--blog-spacing-md) 0;
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .blog-content ul li {
            position: relative;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            background: #ffffff;
            font-size: 1.05rem;
            color: var(--blog-text-color);
            display: flex;
            align-items: flex-start;
            line-height: 1.1;
            font-weight: 400;
        }

        /* Professional checkmark */
        .blog-content ul li::before {
            content: '✓';
            position: absolute;
            left: 0.75rem;
            top: 0.9rem;
            width: 1rem;
            height: 1rem;
            background: var(--blog-accent-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.7rem;
            flex-shrink: 0;
        }

        /* Focus States for Accessibility */
        .blog-content ul li:focus-within {
            outline: 2px solid var(--blog-accent-color);
            outline-offset: 2px;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 768px) {
            :root {
                --blog-spacing-xl: 1.5rem;
                --blog-spacing-lg: 1.25rem;
                --blog-spacing-md: 1rem;
            }

            .blog-content {
                padding: 0 var(--blog-spacing-sm);
            }

            .blog-content p {
                font-size: 1rem;
                line-height: 1.7;
            }

            .blog-content ul {
                gap: 0.2rem;
            }

            .blog-content ul li {
                padding: 0.6rem 0.75rem 0.6rem 2.25rem;
                font-size: 1rem;
            }

            .blog-content ul li::before {
                left: 0.6rem;
                top: 0.75rem;
                width: 0.9rem;
                height: 0.9rem;
                font-size: 0.65rem;
            }
        }

        @media (max-width: 480px) {
            .blog-content ul li {
                padding: var(--blog-spacing-xs) var(--blog-spacing-xs) var(--blog-spacing-xs) 2.25rem;
                font-size: 0.95rem;
            }

            .blog-content ul li::before {
                left: var(--blog-spacing-xs);
            }
        }

        .reading-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 4px;
            background: #3B82F6;
            z-index: 50;
            transition: width 0.2s ease;
        }

        .image-hover-zoom {
            overflow: hidden;
        }

        .image-hover-zoom img {
            transition: transform 0.5s ease;
        }

        .group:hover .image-hover-zoom img {
            transform: scale(1.1);
        }

        /* Placeholder image styles */
        .placeholder {
            position: relative;
            background: #f3f4f6;
            overflow: hidden;
        }

        .placeholder::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 50%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            100% {
                left: 100%;
            }
        }


        section {
            background-color: rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        section:hover {
            opacity: 1;
            transform: translateY(-5px);
        }

        section>div {
            /*background-color: rgba(255, 255, 255, 0.9);*/
            border-radius: 8px;
            transition: opacity 0.3s ease;
        }

        section:hover>div {
            opacity: 1;
        }

        /* Position "signin" and "signup" buttons to the top right */

        /* Round the corners of buttons with icons */
        button i {
            border-radius: 20px;
            /* Adjust the radius as needed */
        }

        button {
            border-radius: 20px;
            /* Adjust the radius as needed */
        }


        .textarea-zoom {
            transition: transform 0.3s ease;
        }

        .textarea-zoom:focus {
            transform: scale(1.05);
        }

        .text-5xl {
            font-size: 2.5rem;
            /* Default size */
        }


        .textarea-zoom:focus {
            outline: 2px solid #007bff;
            box-shadow: 0 0 5px #007bff;
        }

        @media (max-width: 768px) {
            .text-5xl {
                font-size: 2rem;
                /* Smaller size for smaller screens */
            }
        }

        @media (max-width: 768px) {
            #home {
                height: auto !important;
                min-height: 85vh;
            }

            #home .flex-col {
                padding-bottom: 1.5rem;
            }
        }

        .img-panel-fit {
            max-width: 100%;
            height: auto;
            background-color: white;
        }
    </style>
</head>


<body class="bg-gray-200 text-gray-800 h-full">
    <div class="reading-progress" id="reading-progress"></div>

    <!-- Sticky Menu -->
    <nav
        class="fixed top-0 md:top-2 left-0 md:left-1/2 transform md:-translate-x-1/2 bg-white opacity-90 md:rounded-full shadow-xl px-4 md:px-6 py-1.5 flex items-center justify-between w-full md:w-[90%] md:max-w-5xl z-50">
        <!-- Left: Icon and Title -->
        <a href="../index.html" tooltip="Question Legale">
            <div class="flex items-center space-x-2">
                <!-- Icon: replace emoji with SVG or image if needed -->
                <img src="../img/logos/main1.png" alt="Justice.fr" class="w-6 h-6">
                <div class="font-bold text-sm md:text-lg">QuestionLegale.Info</div>

            </div>
        </a>

        <!-- Mobile Section: Burger and Buttons -->
        <div class="flex items-center md:hidden space-x-3">
            <!-- Buttons: visible on mobile -->
            <a href=/login">
                <button id="mobileSignIn" type="button"
                class="px-2 py-0.5 border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition text-xs
                whitespace-nowrap">
                Se Connecter
                </button>
                </a>
                <!-- Burger menu button -->
                <button id="burgerBtn" type="button" class="focus:outline-none p-0.5" aria-label="Toggle menu">
                    <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
        </div>

        <!-- Center: Menu Items (Desktop) -->
        <div id="menuItems" class="hidden md:flex space-x-6 mx-auto w-full max-w-md justify-center">
            <a href="../index.html#analyse" class="text-gray-700 hover:text-blue-500 font-medium text-sm">Question?</a>
            <a href="/Articles/index.htm" class="text-gray-700 hover:text-blue-500 font-medium text-sm">Articles</a>
            <a href="../index.html#references"
                class="text-gray-700 hover:text-blue-500 font-medium text-sm">References</a>
        </div>

        <!-- Right: Sign In and Sign Up Buttons (Desktop) -->
        <div class="hidden md:flex items-center space-x-2">
            <a href="/login">
                <button id="signInBtn" type="button"
                    class="px-3 py-1 border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition text-sm whitespace-nowrap">
                    Se Connecter
                </button>
            </a>
        </div>
    </nav>

    <!-- Mobile Menu: Only for menu items (dropdown) -->
    <div id="mobileMenu"
        class="fixed top-[45px] left-0 w-full bg-white shadow-md rounded-b-lg p-4 md:hidden hidden z-40">
        <div class="flex flex-col space-y-3">
            <a href="/#analyse" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Question?</a>
            <a href="index.htm" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Articles</a>
            <a href="/#references" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">References</a>
            <a href="/services.html" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Services</a>
        </div>
    </div>

    <!-- Presentation -->
    <section id="home" class="relative mb-12 py-16 min-h-screen">
        <!-- Background image with overlay -->
        <div class="absolute inset-0 presentation-card bg-cover bg-right-top bg-no-repeat" data-aos="fade"
            data-aos-duration="1200">
            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
        </div>

        <div class="container mx-auto px-4 py-8 flex flex-col lg:flex-row gap-8">
            <!-- Main Content -->
            <main class="lg:w-3/4" data-aos="fade-up" data-aos-duration="1000">
                <article class="bg-white rounded-xl shadow-md p-6 lg:p-8">
                    <header class="mb-8">
                        <h1 class="text-4xl font-bold mb-4 text-gray-900">Démarches pour Obtenir une Carte de Séjour</h1>
                        <div class="flex items-center text-gray-600 mb-4">
                            <div class="placeholder w-10 h-10 rounded-full mr-4">
                                <img src="../img/avocat1.png" alt="Auteur" class="w-10 h-10 rounded-full">
                            </div>
                            <div>
                                <p class="font-semibold">QuestionLegale.info</p>
                                <p class="text-sm">Publié le 1 juin 2025 · 10 min de lecture</p>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">#CarteDeSéjour</span>
                            <span class="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">#Immigration</span>
                            <span class="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">#DémarchesAdministratives</span>
                        </div>
                    </header>

                    <div class="blog-content prose lg:prose-lg max-w-none">
                        <div class="placeholder rounded-xl mb-8 aspect-[2/1]">
                            <img src="pict/Démarches pour Obtenir une Carte de Séjour/Designer.jpeg" alt="Image Principale" class="rounded-xl w-full h-full object-cover">
                        </div>
                        <p>L'obtention d'une carte de séjour est une étape fondamentale pour tout ressortissant étranger souhaitant s'établir durablement en France. Ce document officiel atteste de votre droit de résider sur le territoire français et est indispensable pour de nombreuses démarches de la vie quotidienne (emploi, logement, accès aux services publics, etc.). Ce guide détaillé vous accompagnera à travers les différentes étapes clés pour obtenir votre titre de séjour, en fonction de votre situation personnelle.</p>

                        <div class="my-8 text-center bg-white p-4 rounded-lg shadow-md">
                            <img src="../Articles/pict/Démarches pour Obtenir une Carte de Séjour/Démarches pour Obtenir une Carte de Séjour - visual selection.png" alt="Étapes pour obtenir une carte de séjour" class="mx-auto img-panel-fit">
                        </div>

                        <h2 id="section1">Vérifier son éligibilité et choisir le bon titre de séjour</h2>
                        <p>Avant d'entamer toute démarche, il est primordial de déterminer le type de carte de séjour qui correspond à votre situation. La législation française prévoit différentes catégories de titres de séjour, chacune avec ses propres critères d'éligibilité et conditions spécifiques. Les principales catégories incluent :</p>
                        <ul>
                            <li><strong>Carte de séjour "étudiant" :</strong> Pour les personnes venant étudier en France.</li>
                            <li><strong>Carte de séjour "salarié" ou "travailleur temporaire" :</strong> Pour ceux qui viennent travailler en France avec un contrat de travail.</li>
                            <li><strong>Carte de séjour "vie privée et familiale" :</strong> Pour les conjoints de Français, les parents d'enfants français, les bénéficiaires du regroupement familial, etc.</li>
                            <li><strong>Carte de séjour "visiteur" :</strong> Pour les personnes souhaitant séjourner en France sans y travailler.</li>
                            <li><strong>Carte de séjour "entrepreneur/profession libérale" :</strong> Pour ceux qui souhaitent créer ou reprendre une activité économique.</li>
                            <li><strong>Carte de séjour "passeport talent" :</strong> Pour les professionnels qualifiés, chercheurs, artistes, investisseurs, etc.</li>
                        </ul>
                        <p>Pour connaître les conditions précises et les documents requis pour chaque type de titre, il est impératif de consulter le site officiel de l'administration française, <a href="https://www.service-public.fr" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">Service-Public.fr</a>, ou le site de la préfecture de votre lieu de résidence. Une vérification minutieuse de votre éligibilité vous évitera des retards ou un refus de votre dossier.</p>

                        <h2 id="section2">Rassembler les documents nécessaires</h2>
                        <p>La constitution d'un dossier complet et conforme est une étape critique. Tout document manquant ou non conforme peut entraîner un retard significatif dans le traitement de votre demande, voire un refus. Voici une liste des documents couramment exigés, à adapter selon le type de titre de séjour demandé :</p>
                        <ul>
                            <li><strong>Passeport valide :</strong> Original et copies de toutes les pages comportant des informations (identité, visas, cachets d'entrée).</li>
                            <li><strong>Visa de long séjour (VLS-TS) :</strong> Si applicable, avec la vignette OFII validée.</li>
                            <li><strong>Extrait d'acte de naissance :</strong> Avec filiation, traduit par un traducteur assermenté si nécessaire.</li>
                            <li><strong>Justificatif de domicile :</strong> Datant de moins de trois mois (quittance de loyer, facture d'électricité, attestation d'hébergement, etc.).</li>
                            <li><strong>Photos d'identité récentes :</strong> Aux normes françaises (moins de 6 mois, fond clair, visage dégagé).</li>
                            <li><strong>Formulaire de demande de carte de séjour :</strong> Dûment rempli et signé, téléchargeable sur le site de la préfecture.</li>
                            <li><strong>Justificatifs de ressources :</strong> Bulletins de salaire, relevés bancaires, attestation de bourse, etc., prouvant des moyens suffisants pour vivre en France.</li>
                            <li><strong>Justificatifs de votre situation spécifique :</strong>
                                <ul>
                                    <li>Pour les étudiants : Certificat de scolarité, diplômes, relevés de notes.</li>
                                    <li>Pour les salariés : Contrat de travail, fiches de paie, autorisation de travail.</li>
                                    <li>Pour le regroupement familial : Livret de famille, acte de mariage, justificatifs de lien de parenté.</li>
                                    <li>Pour les entrepreneurs : Business plan, justificatifs de création d'entreprise.</li>
                                </ul>
                            </li>
                            <li><strong>Justificatif d'assurance maladie :</strong> Attestation de sécurité sociale ou d'assurance privée.</li>
                            <li><strong>Timbres fiscaux :</strong> Le montant varie selon le type de titre de séjour. Vérifiez le montant exact sur Service-Public.fr.</li>
                        </ul>
                        <p><strong>Conseil important :</strong> Préparez toujours les originaux et plusieurs copies de chaque document. Pour les documents en langue étrangère, une traduction par un traducteur assermenté est obligatoire.</p>

                        <h2 id="section3">Prendre rendez-vous à la préfecture ou sous-préfecture</h2>
                        <p>Une fois votre dossier de documents presque complet, l'étape suivante consiste à prendre rendez-vous auprès de la préfecture ou de la sous-préfecture de votre lieu de résidence. Cette démarche est obligatoire dans la grande majorité des cas pour le dépôt initial de votre demande de titre de séjour.</p>
                        <p><strong>Comment prendre rendez-vous :</strong></p>
                        <ul>
                            <li><strong>En ligne :</strong> La méthode la plus courante est de prendre rendez-vous via le site internet de la préfecture de votre département. Recherchez la section dédiée aux "étrangers en France", "titres de séjour" ou "rendez-vous en ligne".</li>
                            <li><strong>Par téléphone ou sur place :</strong> Dans certains cas spécifiques ou pour des situations particulières, il peut être possible de prendre rendez-vous par téléphone ou directement à l'accueil de la préfecture, mais cela est de plus en plus rare.</li>
                        </ul>
                        <p><strong>Conseils pour la prise de rendez-vous :</strong></p>
                        <ul>
                            <li><strong>Anticipez :</strong> Les délais d'attente pour obtenir un rendez-vous peuvent être très longs, parfois plusieurs mois. Prenez rendez-vous dès que possible, même si votre dossier n'est pas encore entièrement prêt, à condition d'avoir une idée claire des documents à rassembler.</li>
                            <li><strong>Soyez persévérant :</strong> Les créneaux de rendez-vous sont souvent pris d'assaut. N'hésitez pas à consulter le site de la préfecture régulièrement, à différents moments de la journée, car de nouveaux créneaux peuvent être libérés.</li>
                            <li><strong>Préparez les informations :</strong> Lors de la prise de rendez-vous en ligne, vous devrez généralement fournir des informations personnelles (nom, prénom, date de naissance, nationalité, numéro de passeport, etc.) et le motif de votre demande.</li>
                            <li><strong>Confirmez le rendez-vous :</strong> Une fois le rendez-vous pris, vous recevrez une convocation par email. Imprimez-la et conservez-la précieusement, elle vous sera demandée le jour J.</li>
                        </ul>
                        <p>Assurez-vous que la date de votre rendez-vous vous laisse suffisamment de temps pour finaliser la collecte et la traduction de tous les documents nécessaires.</p>

                        <h2 id="section4">Déposer la demande et l'entretien</h2>
                        <p>Le jour de votre rendez-vous, présentez-vous à la préfecture ou sous-préfecture à l'heure indiquée, muni de votre convocation et de l'intégralité de votre dossier (originaux et copies). Voici à quoi vous attendre :</p>
                        <ul>
                            <li><strong>Accueil et vérification du dossier :</strong> Un agent vérifiera la complétude de votre dossier. Il est crucial que tous les documents requis soient présents et conformes. Tout manquement peut entraîner un refus de dépôt ou une demande de pièces complémentaires, prolongeant ainsi le délai de traitement.</li>
                            <li><strong>Entretien :</strong> L'agent pourra vous poser des questions sur votre situation personnelle, votre parcours, le motif de votre demande de séjour, vos ressources, etc. Répondez avec calme, honnêteté et précision. L'objectif est de s'assurer de la cohérence de votre demande et de la véracité des informations fournies.</li>
                            <li><strong>Prise d'empreintes digitales et de photographie :</strong> Dans la plupart des cas, vos empreintes digitales et une photographie seront prises sur place pour être intégrées à votre titre de séjour.</li>
                            <li><strong>Remise d'un récépissé :</strong> Si votre dossier est accepté, un récépissé de demande de titre de séjour vous sera remis. Ce document est très important : il atteste du dépôt de votre demande et vous autorise à séjourner légalement en France pendant la durée de son validité, en attendant la décision de la préfecture. Il peut également vous autoriser à travailler, selon le type de titre de séjour demandé.</li>
                        </ul>
                        <p>Conservez précieusement ce récépissé, il est votre preuve de séjour régulier en France.</p>

                        <h2 id="section5">Suivi de la demande et attente de la décision</h2>
                        <p>Une fois votre dossier déposé et le récépissé en main, commence la période d'attente de la décision de la préfecture. Le délai de traitement peut varier considérablement en fonction du type de titre de séjour demandé, de la complexité de votre dossier et de la charge de travail de la préfecture. Il peut aller de quelques semaines à plusieurs mois, voire plus d'un an pour certaines situations complexes.</p>
                        <p><strong>Comment suivre l'avancement de votre dossier :</strong></p>
                        <ul>
                            <li><strong>Espace personnel en ligne :</strong> Si vous avez effectué une pré-demande en ligne ou si la préfecture utilise une plateforme numérique, vous pourrez généralement suivre l'état d'avancement de votre dossier via votre espace personnel.</li>
                            <li><strong>Notifications par courrier ou email :</strong> La préfecture vous contactera par courrier ou par email pour toute demande de complément d'information ou pour vous informer de la décision. Assurez-vous que vos coordonnées sont à jour.</li>
                            <li><strong>Absence de nouvelles :</strong> Si le délai de traitement semble anormalement long et que vous n'avez aucune nouvelle, vous pouvez contacter la préfecture par les moyens mis à disposition (téléphone, formulaire de contact en ligne). Évitez les relances trop fréquentes qui pourraient surcharger les services.</li>
                        </ul>
                        <p><strong>Demande de documents complémentaires :</strong></p>
                        <p>Il est fréquent que la préfecture demande des documents complémentaires après le dépôt initial. Répondez à ces demandes dans les délais impartis et fournissez les pièces exactes demandées pour éviter un refus ou un allongement des délais.</p>
                        <p><strong>Décisions possibles :</strong></p>
                        <ul>
                            <li><strong>Acceptation :</strong> Votre demande est acceptée. Vous recevrez une convocation pour retirer votre titre de séjour.</li>
                            <li><strong>Refus :</strong> Votre demande est refusée. La décision doit être motivée. Vous disposez de voies de recours (voir section "Recours en cas de refus").</li>
                            <li><strong>Silence de l'administration :</strong> Après un certain délai (généralement 4 mois), l'absence de réponse de la préfecture peut valoir décision implicite de rejet (sauf exceptions).</li>
                        </ul>

                        <h2 id="section6">Récupérer votre carte de séjour</h2>
                        <p>Si votre demande est acceptée, vous serez informé de la date à laquelle vous pourrez récupérer votre carte de séjour. Il est important de vous rendre à la préfecture à la date indiquée, muni de votre pièce d'identité et de tout document supplémentaire qui pourrait être requis.</p>

                        <h2 id="section7">Conclusion</h2>
                        <p>Obtenir une carte de séjour en France nécessite de suivre plusieurs étapes, allant de la vérification de l'éligibilité à la récupération du document final. En vous préparant soigneusement et en rassemblant tous les documents nécessaires, vous augmenterez vos chances de succès dans cette démarche administrative.</p>
                    </div>
  
                </article>
            </main>
            <!-- Image Placeholder -->
            <aside class="lg:w-1/3" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="200">
                <div class="bg-white rounded-xl shadow-md p-6 sticky top-20 h-fit">
                    <h2 class="text-xl font-bold mb-6 text-gray-900">Sommaire</h2>
                    <div class="space-y-6">
                        <!-- Related Post Cards -->
                        <a href="#section1" class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 group font-bold">
                            <i class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Vérifier son éligibilité et choisir le bon titre de séjour
                        </a>
                        <a href="#section2" class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 group font-bold">
                            <i class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Rassembler les documents nécessaires
                        </a>
                        <a href="#section3" class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 group font-bold">
                            <i class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Prendre rendez-vous à la préfecture ou sous-préfecture
                        </a>
                        <a href="#section4" class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 group font-bold">
                            <i class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Déposer la demande et l'entretien
                        </a>
                        <a href="#section5" class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 group font-bold">
                            <i class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Suivi de la demande et attente de la décision
                        </a>
                        <a href="#section6" class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 group font-bold">
                            <i class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Récupérer votre carte de séjour
                        </a>
                        <a href="#section7" class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 group font-bold">
                            <i class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Conclusion
                        </a>
                    </div>
                </div>
                <!-- New Sticky Panel for Advertisement -->
                <div class="bg-white rounded-xl shadow-md p-4 mt-6 text-center">
                    <a href="https://www.questionlegale.info" target="_blank" rel="noopener noreferrer">
                        <img src="../../img/pub/pub1_QL.jpg" alt="Publicité QuestionLegale.info" class="h-auto rounded-lg mx-auto block">
                    </a>
                </div>
            </aside>
        </div>
    </section>


   
    <!-- Footer -->
    <footer class="bg-gradient-to-b from-gray-100 to-gray-200 text-gray-800" data-aos="fade-up">

        <!-- Main Footer Content -->
        <div class="max-w-7xl mx-auto px-4 py-16 md:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-10">
                <!-- Column 1: About -->
                <div>
                    <div class="mb-6">
                        <img src="../img/logos/main1.png" alt="QuestionLegale.info" class="h-10 mb-4">
                        <h3 class="text-xl font-bold text-gray-800">QuestionLegale.Info</h3>
                    </div>
                    <p class="text-gray-600 mb-6">Votre plateforme d'information juridique utilisant l'intelligence
                        artificielle pour répondre à vos questions légales en France.</p>


                </div>

                <!-- Column 2: Menu -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">Navigation</h4>
                    <ul class="space-y-3">
                        <li>
                            <a href="../#analyse"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Poser une question
                            </a>
                        </li>
                        <li>
                            <a href="/Articles/index.htm"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Les Articles
                            </a>
                        </li>
                        <li>
                            <a href="../mentions.html"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Mentions légales
                            </a>
                        </li>
                        <li>
                            <a href="../#professionnels"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Trouver un Professionnel
                            </a>
                        </li>
                        <li>
                            <a href="../#utiles"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Sites utiles
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Column 3: À propos -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">À propos</h4>
                    <ul class="space-y-3">
                        <li>
                            <a href="../mentions.html"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Mentions légales
                            </a>
                        </li>
                         <li>
                            <a href="../mentions.html"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                               Politique de confidentialité
                            </a>
                        </li>
                        <li>
                            <a href="../mentions.html"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Conditions d'utilisation
                            </a>
                        </li>
                        <li>
                            <a href="../services.html"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Services destinés aux professionnels
                            </a>
                        </li>
                        <li>
                            <a href="../services.html"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Promotion sur le site web
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Column 4: Contact -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">Contact</h4>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <i class="fas fa-map-marker-alt mt-1 mr-3 text-blue-700"></i>
                            <span class="text-gray-600">75003 Paris, France</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-3 text-blue-700"></i>
                            <span
                                class="text-gray-600 hover:text-blue-700 transition-colors duration-300">contact (Chez) questionlegale.info</span>
                        </li>
                        <!-- <li class="flex items-center">
                            <i class="fas fa-phone-alt mr-3 text-blue-700"></i>
                            <a href="tel:+33123456789"
                                class="text-gray-600 hover:text-blue-700 transition-colors duration-300">+33 1 23 45 67
                                89</a>
                        </li> -->
                        <li class="flex items-center">
                            <i class="fas fa-clock mr-3 text-blue-700"></i>
                            <span class="text-gray-600">Lun - Ven: 9h00 - 18h00</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

      <!-- Bottom Bar -->
    <div class="bg-gray-800 text-gray-300 py-6">
      <div class="max-w-7xl mx-auto px-4 md:px-8 flex flex-col md:flex-row justify-between items-center">
        <div class="mb-4 md:mb-0 text-center md:text-left">
          <p>&copy; 2025 QuestionLegale.Info. Tous droits réservés.</p>
        </div>

        <div class="flex items-center">
          <span class="text-sm mr-2">Made with</span>
          <span class="text-red-500 mx-1">❤</span>
          <span class="text-sm">
            by <a href="https://www.ladorure.online"
              class="text-blue-400 hover:text-blue-300 transition-colors duration-300"> LaDorure
            </a>
          </span>
        </div>

        <div class="hidden md:flex items-center mt-4 md:mt-0">
          <a href="mentions.html#mentions" class="text-gray-400 hover:text-white mx-2 transition-colors duration-300">
            Mentions légales
          </a>
          <span class="text-gray-600">|</span>
        </div>
      </div>
    </div> 

        <!-- Back to Top Button -->
        <a href="#" id="back-to-top" aria-label="Back to top"
            class="fixed bottom-8 right-8 bg-blue-700 text-white p-3 rounded-full shadow-lg hover:bg-blue-800 transition-colors duration-300 opacity-0 invisible">
            <i class="fas fa-arrow-up"></i>
        </a> 

        <script>
            // Back to top button functionality
            const backToTopButton = document.getElementById('back-to-top');

            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    backToTopButton.classList.remove('opacity-0', 'invisible');
                    backToTopButton.classList.add('opacity-100', 'visible');
                } else {
                    backToTopButton.classList.remove('opacity-100', 'visible');
                    backToTopButton.classList.add('opacity-0', 'invisible');
                }
            });

            backToTopButton.addEventListener('click', (e) => {
                e.preventDefault();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        </script>
    </footer>

    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="js/menu.js"></script>
    <script>
        // Enhanced AOS initialization with more options
        AOS.init({
            easing: 'ease-out-cubic',
            duration: 1000,
            once: false,
            mirror: true,
            anchorPlacement: 'top-bottom',
            offset: 120,
            delay: 100,
            disable: 'mobile'
        });

        // Additional interactive effects
        document.querySelectorAll('.hover-zoom').forEach(link => {
            link.addEventListener('mouseenter', (e) => {
                e.target.style.transition = 'transform 0.3s ease, color 0.3s ease';
            });
        });

        // Refresh AOS on window resize for better responsiveness
        window.addEventListener('resize', function () {
            AOS.refresh();
        });

        function toggleFaq(id) {
            const content = document.getElementById(id);
            const icon = document.getElementById(id + '-icon');

            // Toggle content visibility
            content.classList.toggle('hidden');

            // Rotate icon when expanded
            if (content.classList.contains('hidden')) {
                icon.classList.remove('rotate-180');
            } else {
                icon.classList.add('rotate-180');
            }
        }
    </script>
</body>
</html>
