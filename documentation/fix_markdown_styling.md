# Markdown Styling Fix Documentation

## Problem Analysis
The `.markdown-content` class styles are not being applied to dynamically generated content in the `ReponseLegale.vue` component. This occurs because:

```mermaid
graph TD
    A[v-html directive] --> B[Dynamically inserted HTML]
    C[Scoped CSS] --> D[Doesn't apply to dynamic content]
    B --> E[No styles applied]
```

## Root Cause
1. Vue's scoped CSS doesn't target dynamically inserted HTML
2. No global CSS rules exist for `.markdown-content`
3. The component contains no specific styling for markdown elements

## Proposed Solution
```mermaid
graph LR
    A[Create global markdown.css] --> B[Define base styles]
    C[Import in main.js] --> D[Apply globally]
    E[Add deep selector] --> F[Override scoping limitation]
    G[Test rendering] --> H[Verify styling]
```

## Implementation Steps

### 1. Create markdown.css
Path: `src/markdown.css`
```css
/* MARKDOWN CONTENT STYLES */
.markdown-content {
  line-height: 1.8;
  font-size: 1.1rem;
  color: #374151;
}

.markdown-content h2 {
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 0.5rem;
  margin-top: 2rem;
  margin-bottom: 1.5rem;
  color: #1e293b;
}

.markdown-content h3 {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  color: #334155;
}

.markdown-content p {
  margin-bottom: 1.2rem;
}

.markdown-content ul, 
.markdown-content ol {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.markdown-content li {
  margin-bottom: 0.5rem;
}

.markdown-content pre {
  background: #f8fafc;
  padding: 1.2rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin-bottom: 1.5rem;
  border: 1px solid #e2e8f0;
}

.markdown-content code {
  font-family: 'Roboto Mono', monospace;
  background-color: #f1f5f9;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-size: 0.9rem;
}

.markdown-content blockquote {
  border-left: 4px solid #cbd5e1;
  padding-left: 1rem;
  margin-left: 0;
  margin-bottom: 1.5rem;
  color: #475569;
  font-style: italic;
}
```

### 2. Import in main.js
Add to `src/main.js`:
```js
import './markdown.css';
```

### 3. Enhance component styling
Add to `ReponseLegale.vue` (inside scoped style block):
```css
:deep(.markdown-content) {
  /* Add component-specific overrides here if needed */
  padding: 1.5rem;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}
```

### 4. Update ReponseLegale.vue
Add deep selector for specific elements:
```css
:deep(.markdown-content h2) {
  color: #1d4ed8;
}
```

## Alternative Approaches
1. **Vue Markdown Component**  
   Use a dedicated markdown renderer component instead of v-html
2. **CSS-in-JS**  
   Implement dynamic style injection
3. **Unscoped Styles**  
   Add non-scoped style block in component (not recommended)

## Verification Plan
1. Render sample markdown with:
   - Headers
   - Lists
   - Code blocks
   - Paragraphs
2. Check styling in different viewports
3. Verify print and PDF export formats

## Implementation Status
Pending - To be implemented in Code mode