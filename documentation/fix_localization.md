# Professionals Component Localization Update Plan

## Objective
Update the Professionals component to:
1. Use IP location API for city detection at component load
2. Build dynamic links using detected city
3. Replace button elements with anchor tags
4. Populate href attributes during component initialization

## Current Implementation Analysis
- Location is currently fetched on button click via `openProfessionalSearch` method
- Links are built dynamically when user clicks buttons
- Uses `getLocationByIP` from `src/utils/location.js`
- Buttons have click handlers that open windows

## Required Changes
1. Move location fetching to component load time
2. Pre-build links during initialization
3. Replace buttons with anchor tags
4. Maintain error handling for location failures

## Implementation Plan

```mermaid
graph TD
    A[Component Initialization] --> B[Import location util]
    A --> C[Create reactive link variables]
    A --> D[Add onMounted hook]
    D --> E[Call getLocationByIP]
    E --> F{Build links}
    F -->|Success| G[Set hrefs with detected city]
    F -->|Error| H[Set hrefs with generic URLs]
    I[Template Update] --> J[Replace buttons with anchor tags]
    J --> K[Bind href attributes to variables]
```

## Detailed Steps

### 1. Update Script Section (Professionals.vue)

```javascript
import { ref, onMounted } from 'vue';
import { getLocationByIP } from '@/utils/location';

// Create reactive variables for links
const lawyerGoogleLink = ref('');
const lawyerPagesJaunesLink = ref('');
const notaryGoogleLink = ref('');
const notaryPagesJaunesLink = ref('');

onMounted(async () => {
  try {
    const ville = await getLocationByIP();
    
    // Build links with detected city
    lawyerGoogleLink.value = `https://www.google.com/maps/search/avocat+${encodeURIComponent(ville)}`;
    lawyerPagesJaunesLink.value = `https://www.pagesjaunes.fr/carte/recherche?quoiqui=avocat&ou=${encodeURIComponent(ville)}`;
    notaryGoogleLink.value = `https://www.google.com/maps/search/notaire+${encodeURIComponent(ville)}`;
    notaryPagesJaunesLink.value = `https://www.pagesjaunes.fr/carte/recherche?quoiqui=notaire&ou=${encodeURIComponent(ville)}`;
  } catch (error) {
    console.error('Location error:', error);
    // Fallback to generic links
    lawyerGoogleLink.value = 'https://www.google.com/maps/search/avocat';
    lawyerPagesJaunesLink.value = 'https://www.pagesjaunes.fr/annuaire/chercherlespros?quoiqui=avocat';
    notaryGoogleLink.value = 'https://www.google.com/maps/search/notaire';
    notaryPagesJaunesLink.value = 'https://www.pagesjaunes.fr/annuaire/chercherlespros?quoiqui=notaire';
  }
});

// Remove the existing openProfessionalSearch method
```

### 2. Update Template (Professionals.vue)

Replace each button pair with anchor tags:

```html
<!-- For Avocat - Google Maps -->
<a 
  :href="lawyerGoogleLink" 
  target="_blank"
  class="w-full mb-4 block"
>
  <span class="flex group relative w-full bg-white text-green-600 border border-green-600 font-semibold px-2 py-1 rounded-full transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-50 hover:bg-green-600 hover:text-white justify-center items-center">
    <!-- SVG icon and text -->
  </span>
</a>

<!-- For Avocat - Pages Jaunes -->
<a 
  :href="lawyerPagesJaunesLink" 
  target="_blank"
  class="w-full block"
>
  <span class="flex group relative w-full bg-white text-yellow-600 border border-yellow-600 font-semibold px-2 py-1 rounded-full transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-opacity-50 hover:bg-yellow-600 hover:text-white justify-center items-center">
    <!-- SVG icon and text -->
  </span>
</a>

<!-- Repeat same pattern for Notaire links -->
```

### 3. Remove Unused Code
- Delete the `openProfessionalSearch` method
- Remove all `@click` handlers from buttons

## Benefits
- Links pre-built at component load (faster user experience)
- Cleaner code structure without click handlers
- Maintains existing styling and UX
- Preserves error handling for location failures

## Validation Steps
1. Test component with successful location detection
2. Verify links contain detected city name
3. Test with location errors (should use generic links)
4. Check all links open correctly in new tabs
5. Confirm styling remains consistent