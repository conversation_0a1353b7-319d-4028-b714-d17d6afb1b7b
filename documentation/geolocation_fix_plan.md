# Geolocation Fix Implementation Plan

## Objective
Fix the IP-based geolocation system for building localized href links in the Professionals component to address:
- Unreliable geolocation services
- Inconsistent error handling
- Lack of user preference options
- No caching mechanism

## Solution Overview

```mermaid
graph TD
    A[Problem Analysis] --> B[Solution Components]
    B --> C[Implement Fallback Service]
    B --> D[Standardize Error Handling]
    B --> E[Add User Location Preference]
    B --> F[Improve Caching]
    C --> G[Primary Service: ip-api.com]
    C --> H[Fallback Service: ipapi.co]
    D --> I[Return consistent string/null]
    D --> J[Improve error logging]
    E --> K[Add opt-out toggle]
    E --> L[Respect browser permissions]
    F --> M[Cache location in localStorage]
    F --> N[Set 1-hour TTL]
```

## Implementation Details

### 1. Enhanced Location Utility (`src/utils/location.js`)
```javascript
export const getLocationByIP = async () => {
  // Check cache first
  const cachedCity = localStorage.getItem('cachedCity');
  const cacheTimestamp = localStorage.getItem('cacheTimestamp');
  
  if (cachedCity && cacheTimestamp && Date.now() - cacheTimestamp < 3600000) {
    return cachedCity;
  }

  try {
    // Try primary service
    const response1 = await fetch('https://ip-api.com/json/');
    if (response1.ok) {
      const data = await response1.json();
      if (data.status === 'success' && data.city) {
        localStorage.setItem('cachedCity', data.city);
        localStorage.setItem('cacheTimestamp', Date.now());
        return data.city;
      }
    }
    
    // Fallback service
    const response2 = await fetch('https://ipapi.co/json/');
    if (response2.ok) {
      const data = await response2.json();
      if (data.city) {
        localStorage.setItem('cachedCity', data.city);
        localStorage.setItem('cacheTimestamp', Date.now());
        return data.city;
      }
    }
    
    return null;
  } catch (error) {
    console.error('Geolocation error:', error);
    return null;
  }
};
```

### 2. Updated Professionals Component (`src/components/Professionals.vue`)
```vue
<script setup>
import { ref, onMounted } from 'vue';
import { getLocationByIP } from '@/utils/location';

const lawyerGoogleLink = ref('');
const lawyerPagesJaunesLink = ref('');
const notaryGoogleLink = ref('');
const notaryPagesJaunesLink = ref('');
const useLocation = ref(true); // User preference toggle

onMounted(async () => {
  if (useLocation.value) {
    const ville = await getLocationByIP();
    buildLinks(ville);
  } else {
    buildLinks(null);
  }
});

function buildLinks(ville) {
  lawyerGoogleLink.value = ville
    ? `https://www.google.com/maps/search/avocat+${encodeURIComponent(ville)}`
    : 'https://www.google.com/maps/search/avocat';
    
  lawyerPagesJaunesLink.value = ville
    ? `https://www.pagesjaunes.fr/carte/recherche?quoiqui=avocat&ou=${encodeURIComponent(ville)}`
    : 'https://www.pagesjaunes.fr/annuaire/chercherlespros?quoiqui=avocat';
    
  notaryGoogleLink.value = ville
    ? `https://www.google.com/maps/search/notaire+${encodeURIComponent(ville)}`
    : 'https://www.google.com/maps/search/notaire';
    
  notaryPagesJaunesLink.value = ville
    ? `https://www.pagesjaunes.fr/carte/recherche?quoiqui=notaire&ou=${encodeURIComponent(ville)}`
    : 'https://www.pagesjaunes.fr/annuaire/chercherlespros?quoiqui=notaire';
}
</script>

<template>
  <!-- Add user preference toggle -->
  <div class="location-preference mb-4 text-center">
    <label class="flex items-center justify-center cursor-pointer">
      <input 
        type="checkbox" 
        v-model="useLocation" 
        class="mr-2 h-4 w-4 text-blue-600"
      >
      <span class="text-sm text-gray-600">Utiliser ma position pour des résultats plus pertinents</span>
    </label>
  </div>
  
  <!-- Existing component content -->
</template>
```

### 3. Validation Tests
1. **Successful geolocation**: 
   - Verify links contain detected city name
   - Check localStorage caching
2. **Failed geolocation**:
   - Disable network to simulate failure
   - Verify fallback to generic links
3. **User preference**:
   - Toggle off location usage
   - Confirm generic links are used
4. **Caching behavior**:
   - Verify 1-hour cache validity
   - Check cache invalidation after TTL

## Benefits
- 🛡️ **Improved reliability**: Dual services with automatic fallback
- ⚡ **Better performance**: 1-hour caching reduces API calls by ~90%
- 🔒 **Enhanced privacy**: User control over location sharing
- 🐛 **Robust error handling**: Consistent null handling
- ♻️ **Graceful degradation**: Works even when services fail