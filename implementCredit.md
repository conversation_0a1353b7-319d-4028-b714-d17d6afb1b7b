# Implementing a Credit System with API Key Generation in Supabase

This comprehensive guide outlines a step-by-step approach for implementing a credit system in Supabase that automatically awards 3 credits to users upon registration and generates unique API keys for each user. By following this structured implementation, you'll create a secure, scalable system that can serve as the foundation for various credit-based features in your application.

## Understanding the Architecture

Before diving into implementation, it's important to understand the overall architecture of the system we're building. The solution will consist of several key components: a credits table to track user balances, an API key generation mechanism, database triggers for automatic credit allocation, and secure storage for sensitive key information. This approach leverages Supabase's powerful features including PostgreSQL extensions, database triggers, and the Vault for secure secret storage.

## Setting Up the Database Schema

### Step 1: Create the User Credits Table

First, let's create a table to store user credits. Open the Supabase SQL Editor and execute the following commands:

```sql
-- Create a table to store user credits
CREATE TABLE public.user_credits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  credits INTEGER DEFAULT 3 NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  CONSTRAINT credits_non_negative CHECK (credits >= 0)
);

-- Create an index on user_id for faster lookups
CREATE INDEX idx_user_credits_user_id ON public.user_credits(user_id);

-- Set up Row Level Security
ALTER TABLE public.user_credits ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to read only their own credit data
CREATE POLICY "Users can view their own credits" 
  ON public.user_credits 
  FOR SELECT 
  USING (auth.uid() = user_id);

-- Create policy to allow the system to insert/update credit records
CREATE POLICY "System can manage credit records" 
  ON public.user_credits 
  FOR ALL 
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);
```

### Step 2: Create the API Keys Table

Now, let's create a table to store user API keys:

```sql
-- Enable the pgcrypto extension for cryptographic functions
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Create a table to store API keys
CREATE TABLE public.api_keys (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  key_hash TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  last_used_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT TRUE NOT NULL,
  UNIQUE(user_id, name)
);

-- Create an index on key_hash for faster lookups
CREATE INDEX idx_api_keys_key_hash ON public.api_keys(key_hash);

-- Set up Row Level Security
ALTER TABLE public.api_keys ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to view their own API keys
CREATE POLICY "Users can view their own API keys" 
  ON public.api_keys 
  FOR SELECT 
  USING (auth.uid() = user_id);

-- Create policy to allow users to manage their own API keys
CREATE POLICY "Users can manage their own API keys" 
  ON public.api_keys 
  FOR ALL 
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);
```

## Setting Up Vault for Secure Key Storage

### Step 3: Configure Supabase Vault

Supabase Vault provides a secure way to store sensitive information like API keys. Let's set it up:

```sql
-- Enable the Vault extension (already available in Supabase)
-- No need to explicitly create it as it's available by default

-- Create a function to store a secret in Vault
CREATE OR REPLACE FUNCTION public.store_in_vault(
  secret_name TEXT,
  secret_value TEXT
) RETURNS VOID AS $$
BEGIN
  PERFORM vault.create_secret(secret_name, secret_value);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to retrieve a secret from Vault
CREATE OR REPLACE FUNCTION public.get_from_vault(
  secret_name TEXT
) RETURNS TEXT AS $$
DECLARE
  secret_value TEXT;
BEGIN
  SELECT decrypted_secret INTO secret_value 
  FROM vault.decrypted_secrets 
  WHERE name = secret_name;
  
  RETURN secret_value;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Implementing API Key Generation and Management

### Step 4: Create Functions for API Key Generation

Let's implement functions to generate and validate API keys:

```sql
-- Function to generate a new API key for a user
CREATE OR REPLACE FUNCTION public.generate_api_key(
  key_name TEXT
) RETURNS TEXT AS $$
DECLARE
  new_key TEXT;
  key_prefix TEXT;
  user_id UUID;
BEGIN
  -- Get the current user ID
  user_id := auth.uid();
  
  -- Generate a random API key
  key_prefix := 'sk_';
  new_key := key_prefix || encode(gen_random_bytes(24), 'base64');
  
  -- Store the hashed key in the api_keys table
  INSERT INTO public.api_keys (
    user_id,
    name,
    key_hash
  ) VALUES (
    user_id,
    key_name,
    crypt(new_key, gen_salt('bf'))
  );
  
  -- Store the actual key in Vault for potential future use
  PERFORM public.store_in_vault('api_key_' || user_id || '_' || key_name, new_key);
  
  RETURN new_key;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate an API key
CREATE OR REPLACE FUNCTION public.validate_api_key(
  api_key TEXT
) RETURNS UUID AS $$
DECLARE
  matched_user_id UUID;
BEGIN
  -- Find the user associated with this API key
  SELECT user_id INTO matched_user_id
  FROM public.api_keys
  WHERE key_hash = crypt(api_key, key_hash)
    AND is_active = TRUE
    AND (expires_at IS NULL OR expires_at > NOW());
  
  -- Update last_used_at if a valid key was found
  IF matched_user_id IS NOT NULL THEN
    UPDATE public.api_keys
    SET last_used_at = NOW()
    WHERE user_id = matched_user_id
      AND key_hash = crypt(api_key, key_hash);
  END IF;
  
  RETURN matched_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Setting Up Automatic Credit Allocation on Registration

### Step 5: Create Database Triggers

Now, let's create triggers to automatically add credits and generate an API key when a new user registers:

```sql
-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS TRIGGER AS $$
DECLARE
  default_api_key TEXT;
  default_key_name TEXT := 'default';
BEGIN
  -- Create credit record for the new user
  INSERT INTO public.user_credits (user_id, credits)
  VALUES (NEW.id, 3);
  
  -- Generate a default API key for the user
  -- First, we need to temporarily set the auth.uid() to the new user's ID
  PERFORM set_config('request.jwt.claim.sub', NEW.id::text, true);
  
  -- Generate the API key
  default_api_key := public.generate_api_key(default_key_name);
  
  -- Reset the auth context
  PERFORM set_config('request.jwt.claim.sub', '', true);
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to call the function when a new user is created
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

### Step 6: Handle Returning Users

Let's create a function to handle users who unsubscribe and later resubscribe with the same email:

```sql
-- Function to handle returning users
CREATE OR REPLACE FUNCTION public.handle_returning_user() 
RETURNS TRIGGER AS $$
DECLARE
  existing_credits RECORD;
  default_api_key TEXT;
  default_key_name TEXT := 'default';
BEGIN
  -- Check if this user already has a credit record
  SELECT * INTO existing_credits
  FROM public.user_credits
  WHERE user_id = NEW.id;
  
  -- If no existing record, create one with default credits
  IF existing_credits IS NULL THEN
    INSERT INTO public.user_credits (user_id, credits)
    VALUES (NEW.id, 3);
    
    -- Generate a default API key for the user
    -- First, we need to temporarily set the auth.uid() to the new user's ID
    PERFORM set_config('request.jwt.claim.sub', NEW.id::text, true);
    
    -- Generate the API key
    default_api_key := public.generate_api_key(default_key_name);
    
    -- Reset the auth context
    PERFORM set_config('request.jwt.claim.sub', '', true);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Replace the previous trigger with this more comprehensive one
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_returning_user();
```

## Implementing Credit Management Functions

### Step 7: Create Credit Management Functions

Let's create functions to manage user credits:

```sql
-- Function to use credits
CREATE OR REPLACE FUNCTION public.use_credits(
  amount_to_use INTEGER
) RETURNS BOOLEAN AS $$
DECLARE
  current_credits INTEGER;
BEGIN
  -- Get current credits
  SELECT credits INTO current_credits 
  FROM public.user_credits 
  WHERE user_id = auth.uid();
  
  -- Check if user has enough credits
  IF current_credits IS NULL OR current_credits 
  
    Your Credits: {{ credits }}
     0">
      Your API Keys:
      
        {{ key.name }} (Created: {{ formatDate(key.created_at) }})
      
    
    Generate New API Key
  



import { ref, onMounted } from 'vue'
import { supabase } from '../supabase'

export default {
  setup() {
    const credits = ref(0)
    const apiKeys = ref([])
    
    const fetchCredits = async () => {
      const { data, error } = await supabase
        .from('user_credits')
        .select('credits')
        .single()
      
      if (data) {
        credits.value = data.credits
      } else if (error) {
        console.error('Error fetching credits:', error)
      }
    }
    
    const fetchApiKeys = async () => {
      const { data, error } = await supabase
        .from('api_keys')
        .select('id, name, created_at')
        .order('created_at', { ascending: false })
      
      if (data) {
        apiKeys.value = data
      } else if (error) {
        console.error('Error fetching API keys:', error)
      }
    }
    
    const generateNewApiKey = async () => {
      const keyName = `key_${Date.now()}`
      const { data, error } = await supabase
        .rpc('generate_api_key', { key_name: keyName })
      
      if (data) {
        alert(`Your new API key is: ${data}\nPlease save this somewhere safe as you won't be able to see it again.`)
        fetchApiKeys()
      } else if (error) {
        console.error('Error generating API key:', error)
      }
    }
    
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString()
    }
    
    onMounted(() => {
      fetchCredits()
      fetchApiKeys()
    })
    
    return {
      credits,
      apiKeys,
      generateNewApiKey,
      formatDate
    }
  }
}

```

### Step 10: API Key Usage in Client Code

To use the API key for authentication:

```javascript
// src/api.js
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

export const createSupabaseClient = (apiKey = null) => {
  const headers = apiKey ? { apikey: apiKey } : {}
  
  return createClient(supabaseUrl, supabaseAnonKey, {
    headers,
    auth: {
      persistSession: false // Don't persist session for API key usage
    }
  })
}
```

## Testing the Implementation

### Step 11: Test the Complete Flow

1. Register a new user and verify they receive 3 credits and a default API key
2. Test using credits with the `use_credits` function
3. Generate additional API keys and test authentication with them
4. Test the returning user scenario by deleting and recreating a user with the same email

## Security Considerations

### Step 12: Ensure Security Best Practices

1. Use Supabase Vault to securely store API keys[1]
2. Implement proper Row Level Security (RLS) policies on all tables
3. Use cryptographically secure functions like `gen_random_bytes` for API key generation[6]
4. Store only hashed versions of API keys in the database
5. Set appropriate expiration policies for API keys

## Conclusion

By following this step-by-step guide, you've implemented a comprehensive credit system with API key generation in Supabase. The system automatically awards 3 credits to new users upon registration, securely generates and manages API keys, and properly handles returning users. This implementation leverages Supabase's powerful features including database triggers, Row Level Security, and the Vault for secure secret storage.

This foundation can be extended in various ways, such as implementing credit purchases, expiration policies for credits, or different tiers of API access based on credit balance. The modular design allows for flexibility and scalability as your application grows.

Citations:
[1] https://supabase.com/blog/supabase-vault
[2] https://pganalyze.com/docs/api/create-api-key
[3] https://gist.github.com/j4w8n/25d233194877f69c1cbf211de729afb2
[4] https://github.com/kavindukalinga/PostgreSQL-Database-Encryption-using-Pgcrypto
[5] https://stackoverflow.com/questions/79232184/how-to-implement-a-custom-api-key-system-in-supabase-using-only-my-header
[6] https://stackoverflow.com/questions/34377974/cryptographically-random-primary-key-in-postgresql
[7] https://dev.to/acetrondi/how-to-create-api-keys-in-supabase-for-roles-other-than-anon-and-service-1j7f
[8] https://www.postgresql.org/docs/current/pgcrypto.html
[9] https://supabase.com/docs/guides/api/api-keys
[10] https://x-team.com/magazine/storing-secure-passwords-with-postgresql
[11] https://github.com/orgs/supabase/discussions/4419
[12] https://www.timescale.com/learn/postgresql-extensions-pgcrypto
[13] https://www.reddit.com/r/Supabase/comments/1f8auuk/creating_an_api_key_for_a_custom_role/
[14] https://www.crunchydata.com/blog/postgres-pgcrypto
[15] https://supabase.com/docs/guides/api/creating-routes
[16] https://news.ycombinator.com/item?id=34632316
[17] https://supabase.com/docs/guides/api
[18] https://www.frontend-devops.com/blog/working-with-supabase-using-autogenerated-apis
[19] https://www.postgresql.fastware.com/blog/further-protect-your-data-with-pgcrypto
[20] https://www.sahaj.ai/a-practical-guide-to-implementing-sensitive-data-encryption-using-postgres-pgcrypto/

---
Answer from Perplexity: pplx.ai/share