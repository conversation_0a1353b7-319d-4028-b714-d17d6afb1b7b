# Supavite

Une application Vue 3 moderne construite avec Vite et intégration Supabase.

## Fonctionnalités

- Développé avec Vue 3 Composition API
- Développement rapide avec Vite
- Gestion d'état avec Pinia
- Routage avec Vue Router
- Intégration backend Supabase
- Outillage de développement moderne

## Prérequis

- Node.js (v14 ou supérieur)
- npm ou yarn
- Compte et projet Supabase

## Installation

1. C<PERSON><PERSON> le dépôt :
```bash
git clone <url-du-dépôt>
cd supaviteFR
```

2. Installer les dépendances :
```bash
npm install
```

3. Configurer les variables d'environnement :
Créer un fichier `.env` à la racine avec vos identifiants Supabase :
```
VITE_SUPABASE_URL=votre_url_supabase
VITE_SUPABASE_ANON_KEY=votre_clé_anon_supabase
```

## Développement

Pour démarrer le serveur de développement :
```bash
npm run dev
```

L'application sera disponible sur `http://localhost:5173`

## Construction pour la production

Pour créer une build de production :
```bash
npm run build
```

Pour prévisualiser la build de production localement :
```bash
npm run preview
```

## Structure du projet

- `/src` - Code source
- `/public` - Assets statiques
- `/src/components` - Composants Vue
- `/src/stores` - Stores Pinia
- `/src/router` - Configuration Vue Router

## Dépendances

### Dépendances principales
- Vue 3
- Pinia (Gestion d'état)
- Vue Router
- Client JS Supabase

### Dépendances de développement
- Vite
- @vitejs/plugin-vue

## Version
Version actuelle : 0.0.0

## Licence
Ce projet est privé.
