import { fileURLToPath, URL } from 'node:url';
import { defineConfig, loadEnv } from 'vite'; // Import loadEnv
import vue from '@vitejs/plugin-vue';

export default defineConfig(({ mode }) => {
  // Load environment variables based on the current mode
  const env = loadEnv(mode, process.cwd(), 'VITE_');

  // Check if VITE_QUEST_API_URL is defined
  if (!env.VITE_QUEST_API_URL) {
    throw new Error('API_URL is not defined in the .env file. Please define it to use the API proxy.');
  }

  return {
    plugins: [vue()],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    server: {
      proxy: {
        '/legal': {
          target: env.VITE_QUEST_API_URL, // Use the environment variable
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/legal/, '') // Remove /legal prefix
        }
      }
    }
  };
});
