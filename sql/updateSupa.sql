-- Step 1: Create profiles table to store credit and API key information
CREATE TABLE public.profiles (
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  credits INTEGER NOT NULL,
  api_key UUID NOT NULL UNIQUE,
  PRIMARY KEY (user_id)
);

-- Step 2: Create trigger function for new user registrations
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (user_id, credits, api_key)
  VALUES (NEW.id, 3, gen_random_uuid());
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 3: Create authentication trigger
CREATE TRIGGER on_auth_user_created
AFTER INSERT ON auth.users
FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Step 4: Enable Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Step 5: Create access policies
-- Allow users to read their own profile data
CREATE POLICY "User can view own profile" ON public.profiles
FOR SELECT USING (user_id = auth.uid());


--Decrement Credit function
create or replace function decrement_credits(user_uuid uuid) 
returns void as $$
  update profiles 
  set credits = credits - 1 
  where user_id = user_uuid and credits > 0;
$$ language sql;